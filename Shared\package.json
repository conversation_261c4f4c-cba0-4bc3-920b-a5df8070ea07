{"name": "@relativity/shared", "version": "1.1.3", "description": "Relativity Cloud shared React components library for personnel management and common UI elements", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "keywords": ["react", "components", "ui", "relativity", "personnel-management", "typescript"], "author": "Relativity Development Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-org/relativity-common-components.git"}, "bugs": {"url": "https://github.com/your-org/relativity-common-components/issues"}, "homepage": "https://github.com/your-org/relativity-common-components#readme", "scripts": {"build": "tsc", "dev": "tsc --watch"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^23.0.0", "i18next-browser-languagedetector": "^7.0.0", "lucide-react": "^0.525.0", "react-i18next": "^13.0.0", "react-phone-input-2": "^2.15.1", "tailwindcss": "^3.4.0"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/node": "^24.0.3", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0"}}