// Example: Using CommonComponent theme components with next-themes
// This file shows different ways to integrate with next-themes

import React from 'react';
import { useTheme } from 'next-themes';
import { 
  NextThemeToggle,
  createNextThemeComponents 
} from '@relativity/common-components';

// Method 1: Direct usage (most common)
export function SimpleThemeToggle() {
  return (
    <NextThemeToggle 
      variant="select" 
      useTheme={useTheme}
    />
  );
}

// Method 2: Using factory function
const { ThemeToggle, ThemeToggleButton, ThemeSelector } = createNextThemeComponents(useTheme);

export function FactoryExample() {
  return (
    <div className="space-y-4">
      <ThemeToggleButton />
      <ThemeToggle variant="buttons" />
      <ThemeSelector />
    </div>
  );
}

// Method 3: Complete app example
export function AppExample() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Header with theme toggle */}
      <header className="border-b p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold">My Next.js App</h1>
          <NextThemeToggle variant="icon" useTheme={useTheme} />
        </div>
      </header>

      {/* Main content */}
      <main className="p-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Settings section */}
          <section>
            <h2 className="text-lg font-semibold mb-4">Theme Settings</h2>
            <NextThemeToggle 
              variant="cards"
              showDescriptions={true}
              useTheme={useTheme}
              className="max-w-2xl"
            />
          </section>

          {/* Toolbar example */}
          <section>
            <h2 className="text-lg font-semibold mb-4">Toolbar Example</h2>
            <NextThemeToggle 
              variant="buttons"
              size="sm"
              useTheme={useTheme}
            />
          </section>
        </div>
      </main>
    </div>
  );
}

// Method 4: Custom hook with next-themes
export function useAppTheme() {
  const { theme, setTheme, resolvedTheme } = useTheme();
  
  return {
    theme,
    setTheme,
    resolvedTheme,
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
    toggleTheme: () => setTheme(resolvedTheme === 'dark' ? 'light' : 'dark'),
  };
}

export function CustomHookExample() {
  const { isDark, toggleTheme } = useAppTheme();
  
  return (
    <div className={`p-4 ${isDark ? 'bg-gray-900 text-white' : 'bg-white text-black'}`}>
      <p>Current theme is: {isDark ? 'Dark' : 'Light'}</p>
      <button onClick={toggleTheme} className="mt-2 px-4 py-2 border rounded">
        Toggle Theme
      </button>
      
      {/* Still use the component for better UI */}
      <div className="mt-4">
        <NextThemeToggle variant="select" useTheme={useTheme} />
      </div>
    </div>
  );
}
