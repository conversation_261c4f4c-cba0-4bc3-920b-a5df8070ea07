import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Moon, Sun, Monitor } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { useTheme } from "../../theme/ThemeProvider";
const themeOptions = [
    {
        value: "light",
        label: "Light",
        icon: _jsx(Sun, { className: "h-4 w-4" }),
        description: "Light theme"
    },
    {
        value: "dark",
        label: "Dark",
        icon: _jsx(Moon, { className: "h-4 w-4" }),
        description: "Dark theme"
    },
    {
        value: "system",
        label: "System",
        icon: _jsx(Monitor, { className: "h-4 w-4" }),
        description: "System theme"
    }
];
export function ThemeToggleButton() {
    const { theme, setTheme } = useTheme();
    const currentTheme = themeOptions.find(option => option.value === theme);
    return (_jsxs(Select, { value: theme, onValueChange: setTheme, children: [_jsx(SelectTrigger, { className: "w-40 h-9 bg-background border border-input hover:bg-accent hover:text-accent-foreground transition-colors", children: _jsxs("div", { className: "flex items-center gap-2", children: [currentTheme?.icon, _jsx(SelectValue, { placeholder: "Select theme" })] }) }), _jsx(SelectContent, { align: "end", className: "w-48", children: themeOptions.map((option) => (_jsx(SelectItem, { value: option.value, className: "cursor-pointer", children: _jsxs("div", { className: "flex items-center gap-3 py-1", children: [_jsx("div", { className: "text-muted-foreground", children: option.icon }), _jsxs("div", { className: "flex flex-col", children: [_jsx("span", { className: "font-medium", children: option.label }), _jsx("span", { className: "text-xs text-muted-foreground", children: option.description })] })] }) }, option.value))) })] }));
}
// Add default export
export default ThemeToggleButton;
