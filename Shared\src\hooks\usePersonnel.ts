import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "../lib/api";
import { PersonnelRoles } from "../lib/Schemas/Personnel";
import { type Person, type ResetPassword } from "../lib/Schemas/Personnel";
import { toast } from "../component/ui/use-toast";

// Enhanced types to match backend response

export const personnelApi = {
  // Enhanced getPersonnel to support backend parameters
  getPersonnel: async (): Promise<any> => {
    try {
      const response = await api.get("/api/personnel");
      const json = response.data;

      if (!json.success || !Array.isArray(json.data)) {
        throw new Error("Invalid response format");
      }

      // Normalize the personnel data
      const normalizedData = json.data.map((person: any) => ({
        ...person,
        id: person.id ?? person._id, // Normalize id
      }));

      return {
        ...json,
        data: normalizedData,
      };
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to access this resource");
      }
      throw new Error("Failed to fetch personnel");
    }
  },

  // Keep existing methods unchanged for backward compatibility
  getPersonnelSimple: async (): Promise<any> => {
    const response = await personnelApi.getPersonnel();
    if (!response || !Array.isArray(response.data)) {
      throw new Error("You do not have permission to access this resource");
    }

    return { data: response.data, licenseInfo: response.licenseInfo };
  },

  createPersonnel: async (
    data: Omit<Person, "id" | "status" | "hasLogin">
  ): Promise<Person> => {
    try {
      const response = await api.post("/api/personnel", data);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to create personnel");
      }
      throw new Error("Failed to create personnel");
    }
  },

  updatePersonnel: async (
    data: Partial<Person> & { id: string }
  ): Promise<Person> => {
    try {
      const response = await api.put(`/api/personnel/${data.id}`, data);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to update personnel");
      }
      throw new Error("Failed to update personnel");
    }
  },

  deletePersonnel: async (id: string): Promise<void> => {
    try {
      await api.delete(`/api/personnel/${id}`);
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to delete personnel");
      }
      throw new Error("Failed to delete personnel");
    }
  },

  toggleUserStatus: async (id: string): Promise<Person> => {
    try {
      const response = await api.patch(`/api/personnel/${id}/toggle-user`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to toggle user status");
      }
      throw new Error("Failed to toggle user status");
    }
  },

  createLogin: async (id: string): Promise<Person> => {
    try {
      const response = await api.patch(`/api/personnel/${id}/create-login`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to create login");
      }
      throw new Error("Failed to create login");
    }
  },



  getRoles: async (): Promise<PersonnelRoles[]> => {
    try {
      const response = await api.get("/api/personnel/user/roles");
      const json = response.data;

      if (!Array.isArray(json.data)) {
        throw new Error("Invalid roles response format");
      }
      return json.data.map((role: PersonnelRoles) => ({
        id: role._id ?? role.id,
        name: role.name,
        description: role.description,
        permissionsList: role.permissionsList,
      }));
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to access this resource");
      }
      throw new Error("Failed to fetch roles");
    }
  },

  resetPassword: async (
    id: string,
    data: { currentPassword: string; password: string }
  ) => {
    try {
      const response = await api.put(
        `/api/personnel/user/updateUserPassword/${id}`,
        data
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to reset password");
      }
      const errorMessage = error.response?.data?.message || "Failed to reset password";
      throw new Error(errorMessage);
    }
  },
};

export const personnelKeys = {
  all: ["personnel"] as const,
  lists: () => [...personnelKeys.all, "list"] as const,
  list: () => [...personnelKeys.lists()] as const,
  details: () => [...personnelKeys.all, "detail"] as const,
  detail: (id: string) => [...personnelKeys.details(), id] as const,
};

// Enhanced hook that supports full backend functionality
export const usePersonnelWithPagination = () => {
  return useQuery({
    queryKey: personnelKeys.list(),
    queryFn: () => personnelApi.getPersonnel(),
    staleTime: 5 * 60 * 1000,
  });
};

// Keep original hook for backward compatibility - unchanged functionality
export const usePersonnel = () => {
  return useQuery({
    queryKey: personnelKeys.lists(),
    queryFn: personnelApi.getPersonnelSimple,
    staleTime: 5 * 60 * 1000,
  });
};

// Existing mutations remain unchanged
export const useCreatePersonnel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (person: Omit<Person, "id" | "status" | "hasLogin">) =>
      personnelApi.createPersonnel(person),
    onSuccess: (newPerson) => {
      queryClient.setQueryData(personnelKeys.lists(), (old: any) => {
        // Ensure we always return a valid structure
        if (!old || !Array.isArray(old.data)) {
          return {
            data: [newPerson],
            licenseInfo: {},
          };
        }

        return {
          ...old,
          data: [...old.data, newPerson],
        };
      });

      queryClient.invalidateQueries({ queryKey: personnelKeys.lists() });

      // Success toast
      toast({
        title: "Success",
        description: `Personnel ${newPerson.firstName} ${newPerson.lastName} has been created successfully.`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to create personnel:", error);

      // Error toast
      toast({
        title: "Error",
        description: error.message || "Failed to create personnel. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdatePersonnel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (person: Partial<Person> & { id: string }) =>
      personnelApi.updatePersonnel(person),
    onSuccess: (updatedPerson) => {
      queryClient.setQueryData(personnelKeys.lists(), (old: any) => {
        if (!old || !Array.isArray(old.data)) return old;

        return {
          ...old,
          data: old.data.map((p: Person) =>
            p.id === updatedPerson.id ? updatedPerson : p
          ),
        };
      });

      queryClient.invalidateQueries({ queryKey: personnelKeys.all });

      // Success toast
      toast({
        title: "Success",
        description: `Personnel ${updatedPerson.firstName} ${updatedPerson.lastName} has been updated successfully.`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to update personnel:", error);

      // Error toast
      toast({
        title: "Error",
        description: error.message || "Failed to update personnel. Please try again.",
        variant: "destructive",
      });
    },
  });
};



export const useDeletePersonnel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: personnelApi.deletePersonnel,
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData(personnelKeys.lists(), (old: any) => {
        if (!old || !Array.isArray(old.data)) return old;

        return {
          ...old,
          data: old.data.filter((p: Person) => p.id !== deletedId),
        };
      });

      queryClient.invalidateQueries({ queryKey: personnelKeys.lists() });

      // Success toast
      toast({
        title: "Success",
        description: "Personnel has been deleted successfully.",
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to delete personnel:", error);

      // Error toast
      toast({
        title: "Error",
        description: error.message || "Failed to delete personnel. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useToggleUserStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: personnelApi.toggleUserStatus,
    onSuccess: (updatedPerson) => {
      queryClient.setQueryData<Person[]>(
        personnelKeys.lists(),
        (old) =>
          old?.map((p) => (p.id === updatedPerson.id ? updatedPerson : p)) || []
      );

      // Success toast
      toast({
        title: "Success",
        description: `User status for ${updatedPerson.firstName} ${updatedPerson.lastName} has been updated successfully.`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to toggle user status:", error);

      // Error toast
      toast({
        title: "Error",
        description: error.message || "Failed to toggle user status. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useCreateLogin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: personnelApi.createLogin,
    onSuccess: (updatedPerson) => {
      queryClient.setQueryData<Person[]>(
        personnelKeys.lists(),
        (old) =>
          old?.map((p) => (p.id === updatedPerson.id ? updatedPerson : p)) || []
      );

      // Success toast
      toast({
        title: "Success",
        description: `Login has been created successfully for ${updatedPerson.firstName} ${updatedPerson.lastName}.`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to create login:", error);

      // Error toast
      toast({
        title: "Error",
        description: error.message || "Failed to create login. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useRoles = () => {
  return useQuery<PersonnelRoles[]>({
    queryKey: ["roles"],
    queryFn: personnelApi.getRoles,
    staleTime: 10 * 60 * 1000,
  });
};

export const useResetPassword = () => {
  return useMutation({
    mutationFn: ({ id, currentPassword, password }: ResetPassword & { id: string }) =>
      personnelApi.resetPassword(id!, { currentPassword, password }),
    onSuccess: () => {

      // Success toast
      toast({
        title: "Success",
        description: "Password has been reset successfully.",
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to reset password:", error);

      // Error toast
      toast({
        title: "Error",
        description: error.message || "Failed to reset password. Please try again.",
        variant: "destructive",
      });
    },
  });
};
