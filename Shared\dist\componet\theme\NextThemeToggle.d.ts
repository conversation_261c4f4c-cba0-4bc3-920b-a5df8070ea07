interface NextThemeHook {
    theme: string | undefined;
    setTheme: (theme: string) => void;
    resolvedTheme: string | undefined;
    themes: string[];
    systemTheme: string | undefined;
}
interface NextThemeToggleProps {
    variant?: 'select' | 'buttons' | 'cards' | 'icon';
    size?: 'sm' | 'default' | 'lg';
    showLabels?: boolean;
    showDescriptions?: boolean;
    className?: string;
    useTheme: () => NextThemeHook;
}
export declare function NextThemeToggle({ variant, size, showLabels, showDescriptions, className, useTheme, }: NextThemeToggleProps): import("react/jsx-runtime").JSX.Element;
export declare function NextThemeToggleButton({ useTheme }: {
    useTheme: () => NextThemeHook;
}): import("react/jsx-runtime").JSX.Element;
export declare function NextThemeSelector({ useTheme }: {
    useTheme: () => NextThemeHook;
}): import("react/jsx-runtime").JSX.Element;
export declare function NextThemeSwitcher({ useTheme }: {
    useTheme: () => NextThemeHook;
}): import("react/jsx-runtime").JSX.Element;
export default NextThemeToggle;
