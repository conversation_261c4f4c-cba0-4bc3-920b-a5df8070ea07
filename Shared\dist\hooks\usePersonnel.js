import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "../lib/api";
import { toast } from "../component/ui/use-toast";
// Enhanced types to match backend response
export const personnelApi = {
    // Enhanced getPersonnel to support backend parameters
    getPersonnel: async () => {
        try {
            const response = await api.get("/api/personnel");
            const json = response.data;
            if (!json.success || !Array.isArray(json.data)) {
                throw new Error("Invalid response format");
            }
            // Normalize the personnel data
            const normalizedData = json.data.map((person) => ({
                ...person,
                id: person.id ?? person._id, // Normalize id
            }));
            return {
                ...json,
                data: normalizedData,
            };
        }
        catch (error) {
            if (error.response?.status === 403) {
                throw new Error("You do not have permission to access this resource");
            }
            throw new Error("Failed to fetch personnel");
        }
    },
    // Keep existing methods unchanged for backward compatibility
    getPersonnelSimple: async () => {
        const response = await personnelApi.getPersonnel();
        if (!response || !Array.isArray(response.data)) {
            throw new Error("You do not have permission to access this resource");
        }
        return { data: response.data, licenseInfo: response.licenseInfo };
    },
    createPersonnel: async (data) => {
        try {
            const response = await api.post("/api/personnel", data);
            return response.data;
        }
        catch (error) {
            if (error.response?.status === 403) {
                throw new Error("You do not have permission to create personnel");
            }
            throw new Error("Failed to create personnel");
        }
    },
    updatePersonnel: async (data) => {
        try {
            const response = await api.put(`/api/personnel/${data.id}`, data);
            return response.data;
        }
        catch (error) {
            if (error.response?.status === 403) {
                throw new Error("You do not have permission to update personnel");
            }
            throw new Error("Failed to update personnel");
        }
    },
    deletePersonnel: async (id) => {
        try {
            await api.delete(`/api/personnel/${id}`);
        }
        catch (error) {
            if (error.response?.status === 403) {
                throw new Error("You do not have permission to delete personnel");
            }
            throw new Error("Failed to delete personnel");
        }
    },
    toggleUserStatus: async (id) => {
        try {
            const response = await api.patch(`/api/personnel/${id}/toggle-user`);
            return response.data;
        }
        catch (error) {
            if (error.response?.status === 403) {
                throw new Error("You do not have permission to toggle user status");
            }
            throw new Error("Failed to toggle user status");
        }
    },
    createLogin: async (id) => {
        try {
            const response = await api.patch(`/api/personnel/${id}/create-login`);
            return response.data;
        }
        catch (error) {
            if (error.response?.status === 403) {
                throw new Error("You do not have permission to create login");
            }
            throw new Error("Failed to create login");
        }
    },
    getRoles: async () => {
        try {
            const response = await api.get("/api/personnel/user/roles");
            const json = response.data;
            if (!Array.isArray(json.data)) {
                throw new Error("Invalid roles response format");
            }
            return json.data.map((role) => ({
                id: role._id ?? role.id,
                name: role.name,
                description: role.description,
                permissionsList: role.permissionsList,
            }));
        }
        catch (error) {
            if (error.response?.status === 403) {
                throw new Error("You do not have permission to access this resource");
            }
            throw new Error("Failed to fetch roles");
        }
    },
    resetPassword: async (id, data) => {
        try {
            const response = await api.put(`/api/personnel/user/updateUserPassword/${id}`, data);
            return response.data;
        }
        catch (error) {
            if (error.response?.status === 403) {
                throw new Error("You do not have permission to reset password");
            }
            const errorMessage = error.response?.data?.message || "Failed to reset password";
            throw new Error(errorMessage);
        }
    },
};
export const personnelKeys = {
    all: ["personnel"],
    lists: () => [...personnelKeys.all, "list"],
    list: () => [...personnelKeys.lists()],
    details: () => [...personnelKeys.all, "detail"],
    detail: (id) => [...personnelKeys.details(), id],
};
// Enhanced hook that supports full backend functionality
export const usePersonnelWithPagination = () => {
    return useQuery({
        queryKey: personnelKeys.list(),
        queryFn: () => personnelApi.getPersonnel(),
        staleTime: 5 * 60 * 1000,
    });
};
// Keep original hook for backward compatibility - unchanged functionality
export const usePersonnel = () => {
    return useQuery({
        queryKey: personnelKeys.lists(),
        queryFn: personnelApi.getPersonnelSimple,
        staleTime: 5 * 60 * 1000,
    });
};
// Existing mutations remain unchanged
export const useCreatePersonnel = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (person) => personnelApi.createPersonnel(person),
        onSuccess: (newPerson) => {
            queryClient.setQueryData(personnelKeys.lists(), (old) => {
                // Ensure we always return a valid structure
                if (!old || !Array.isArray(old.data)) {
                    return {
                        data: [newPerson],
                        licenseInfo: {},
                    };
                }
                return {
                    ...old,
                    data: [...old.data, newPerson],
                };
            });
            queryClient.invalidateQueries({ queryKey: personnelKeys.lists() });
            // Success toast
            toast({
                title: "Success",
                description: `Personnel ${newPerson.firstName} ${newPerson.lastName} has been created successfully.`,
                variant: "default",
            });
        },
        onError: (error) => {
            console.error("Failed to create personnel:", error);
            // Error toast
            toast({
                title: "Error",
                description: error.message || "Failed to create personnel. Please try again.",
                variant: "destructive",
            });
        },
    });
};
export const useUpdatePersonnel = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (person) => personnelApi.updatePersonnel(person),
        onSuccess: (updatedPerson) => {
            queryClient.setQueryData(personnelKeys.lists(), (old) => {
                if (!old || !Array.isArray(old.data))
                    return old;
                return {
                    ...old,
                    data: old.data.map((p) => p.id === updatedPerson.id ? updatedPerson : p),
                };
            });
            queryClient.invalidateQueries({ queryKey: personnelKeys.all });
            // Success toast
            toast({
                title: "Success",
                description: `Personnel ${updatedPerson.firstName} ${updatedPerson.lastName} has been updated successfully.`,
                variant: "default",
            });
        },
        onError: (error) => {
            console.error("Failed to update personnel:", error);
            // Error toast
            toast({
                title: "Error",
                description: error.message || "Failed to update personnel. Please try again.",
                variant: "destructive",
            });
        },
    });
};
export const useDeletePersonnel = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: personnelApi.deletePersonnel,
        onSuccess: (_, deletedId) => {
            queryClient.setQueryData(personnelKeys.lists(), (old) => {
                if (!old || !Array.isArray(old.data))
                    return old;
                return {
                    ...old,
                    data: old.data.filter((p) => p.id !== deletedId),
                };
            });
            queryClient.invalidateQueries({ queryKey: personnelKeys.lists() });
            // Success toast
            toast({
                title: "Success",
                description: "Personnel has been deleted successfully.",
                variant: "default",
            });
        },
        onError: (error) => {
            console.error("Failed to delete personnel:", error);
            // Error toast
            toast({
                title: "Error",
                description: error.message || "Failed to delete personnel. Please try again.",
                variant: "destructive",
            });
        },
    });
};
export const useToggleUserStatus = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: personnelApi.toggleUserStatus,
        onSuccess: (updatedPerson) => {
            queryClient.setQueryData(personnelKeys.lists(), (old) => old?.map((p) => (p.id === updatedPerson.id ? updatedPerson : p)) || []);
            // Success toast
            toast({
                title: "Success",
                description: `User status for ${updatedPerson.firstName} ${updatedPerson.lastName} has been updated successfully.`,
                variant: "default",
            });
        },
        onError: (error) => {
            console.error("Failed to toggle user status:", error);
            // Error toast
            toast({
                title: "Error",
                description: error.message || "Failed to toggle user status. Please try again.",
                variant: "destructive",
            });
        },
    });
};
export const useCreateLogin = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: personnelApi.createLogin,
        onSuccess: (updatedPerson) => {
            queryClient.setQueryData(personnelKeys.lists(), (old) => old?.map((p) => (p.id === updatedPerson.id ? updatedPerson : p)) || []);
            // Success toast
            toast({
                title: "Success",
                description: `Login has been created successfully for ${updatedPerson.firstName} ${updatedPerson.lastName}.`,
                variant: "default",
            });
        },
        onError: (error) => {
            console.error("Failed to create login:", error);
            // Error toast
            toast({
                title: "Error",
                description: error.message || "Failed to create login. Please try again.",
                variant: "destructive",
            });
        },
    });
};
export const useRoles = () => {
    return useQuery({
        queryKey: ["roles"],
        queryFn: personnelApi.getRoles,
        staleTime: 10 * 60 * 1000,
    });
};
export const useResetPassword = () => {
    return useMutation({
        mutationFn: ({ id, currentPassword, password }) => personnelApi.resetPassword(id, { currentPassword, password }),
        onSuccess: () => {
            // Success toast
            toast({
                title: "Success",
                description: "Password has been reset successfully.",
                variant: "default",
            });
        },
        onError: (error) => {
            console.error("Failed to reset password:", error);
            // Error toast
            toast({
                title: "Error",
                description: error.message || "Failed to reset password. Please try again.",
                variant: "destructive",
            });
        },
    });
};
