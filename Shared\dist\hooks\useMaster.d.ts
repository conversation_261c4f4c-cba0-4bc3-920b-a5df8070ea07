export interface Permission {
    _id: string;
    resource: string;
    action: string;
    description: string;
    createdAt: string;
    showGlobal: boolean;
}
export declare const masterApi: {
    getPermissions: (adminApiUrl: string, showGlobal?: boolean) => Promise<Permission[]>;
    getOrgRolesByOrganization: (apiUrl: string, organizationId?: string) => Promise<any>;
};
export declare const masterKeys: {
    all: readonly ["master"];
    permissions: (adminApiUrl: string) => readonly ["master", "permissions", string];
    orgRoles: (apiUrl: string, organizationId?: string) => readonly ["master", "orgRoles", string, string];
};
export declare const usePermissions: (adminApiUrl: string) => import("@tanstack/react-query").UseQueryResult<Permission[], Error>;
export declare const useOrgPermissions: (adminApiUrl: string) => import("@tanstack/react-query").UseQueryResult<Permission[], Error>;
export declare const useOrgRolesByOrganization: (organizationId?: string, apiUrl?: string) => import("@tanstack/react-query").UseQueryResult<any, Error>;
