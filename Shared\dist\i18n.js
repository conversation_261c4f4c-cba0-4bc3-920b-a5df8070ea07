import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
// Default language resources for common UI elements
export const defaultResources = {
    en: {
        translation: {
            // Theme
            light: "Light",
            dark: "Dark",
            system: "System",
            toggle_theme: "Toggle theme",
            // Language
            language: "Language",
            english: "English",
            spanish: "Spanish",
            french: "French",
            // Common UI
            settings: "Settings",
            user: "User",
            profile: "Profile",
            your_profile: "Your Profile",
            sign_out: "Sign out",
            logout: "Logout",
            cancel: "Cancel",
            save: "Save",
            edit: "Edit",
            delete: "Delete",
            add: "Add",
            remove: "Remove",
            close: "Close",
            open: "Open",
            loading: "Loading...",
            error: "Error",
            success: "Success",
            warning: "Warning",
            info: "Info",
            // Navigation
            home: "Home",
            back: "Back",
            next: "Next",
            previous: "Previous",
            // Form elements
            submit: "Submit",
            reset: "Reset",
            search: "Search",
            filter: "Filter",
            sort: "Sort",
            // Time and dates
            today: "Today",
            yesterday: "Yesterday",
            tomorrow: "Tomorrow",
            // Status
            active: "Active",
            inactive: "Inactive",
            enabled: "Enabled",
            disabled: "Disabled"
        }
    },
    es: {
        translation: {
            // Theme
            light: "Claro",
            dark: "Oscuro",
            system: "Sistema",
            toggle_theme: "Cambiar tema",
            // Language
            language: "Idioma",
            english: "Inglés",
            spanish: "Español",
            french: "Francés",
            // Common UI
            settings: "Configuración",
            user: "Usuario",
            profile: "Perfil",
            your_profile: "Tu Perfil",
            sign_out: "Cerrar sesión",
            logout: "Salir",
            cancel: "Cancelar",
            save: "Guardar",
            edit: "Editar",
            delete: "Eliminar",
            add: "Agregar",
            remove: "Quitar",
            close: "Cerrar",
            open: "Abrir",
            loading: "Cargando...",
            error: "Error",
            success: "Éxito",
            warning: "Advertencia",
            info: "Información",
            // Navigation
            home: "Inicio",
            back: "Atrás",
            next: "Siguiente",
            previous: "Anterior",
            // Form elements
            submit: "Enviar",
            reset: "Restablecer",
            search: "Buscar",
            filter: "Filtrar",
            sort: "Ordenar",
            // Time and dates
            today: "Hoy",
            yesterday: "Ayer",
            tomorrow: "Mañana",
            // Status
            active: "Activo",
            inactive: "Inactivo",
            enabled: "Habilitado",
            disabled: "Deshabilitado"
        }
    },
    fr: {
        translation: {
            // Theme
            light: "Clair",
            dark: "Sombre",
            system: "Système",
            toggle_theme: "Basculer le thème",
            // Language
            language: "Langue",
            english: "Anglais",
            spanish: "Espagnol",
            french: "Français",
            // Common UI
            settings: "Paramètres",
            user: "Utilisateur",
            profile: "Profil",
            your_profile: "Votre Profil",
            sign_out: "Se déconnecter",
            logout: "Déconnexion",
            cancel: "Annuler",
            save: "Enregistrer",
            edit: "Modifier",
            delete: "Supprimer",
            add: "Ajouter",
            remove: "Retirer",
            close: "Fermer",
            open: "Ouvrir",
            loading: "Chargement...",
            error: "Erreur",
            success: "Succès",
            warning: "Avertissement",
            info: "Information",
            // Navigation
            home: "Accueil",
            back: "Retour",
            next: "Suivant",
            previous: "Précédent",
            // Form elements
            submit: "Soumettre",
            reset: "Réinitialiser",
            search: "Rechercher",
            filter: "Filtrer",
            sort: "Trier",
            // Time and dates
            today: "Aujourd'hui",
            yesterday: "Hier",
            tomorrow: "Demain",
            // Status
            active: "Actif",
            inactive: "Inactif",
            enabled: "Activé",
            disabled: "Désactivé"
        }
    }
};
// Default configuration
const defaultConfig = {
    fallbackLng: 'en',
    debug: false,
    resources: defaultResources,
    detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
    }
};
// Initialize i18n with default configuration
export const initI18n = (customConfig) => {
    const config = { ...defaultConfig, ...customConfig };
    // Merge resources if custom resources provided
    if (customConfig?.resources) {
        config.resources = mergeResources(defaultResources, customConfig.resources);
    }
    return i18n
        .use(LanguageDetector)
        .use(initReactI18next)
        .init({
        ...config,
        interpolation: {
            escapeValue: false,
        }
    });
};
// Utility function to merge language resources
export const mergeResources = (defaultRes, customRes) => {
    const merged = {};
    // Get all unique language keys
    const allLanguages = new Set([
        ...Object.keys(defaultRes),
        ...Object.keys(customRes)
    ]);
    allLanguages.forEach(lang => {
        merged[lang] = {
            translation: {
                ...defaultRes[lang]?.translation || {},
                ...customRes[lang]?.translation || {}
            }
        };
    });
    return merged;
};
// Hook for i18n functionality
export const useI18n = () => {
    const { t, i18n: i18nInstance } = require('react-i18next');
    const changeLanguage = (language) => {
        i18nInstance.changeLanguage(language);
    };
    const getCurrentLanguage = () => i18nInstance.language;
    const getAvailableLanguages = () => Object.keys(i18nInstance.options.resources || {});
    return {
        t,
        changeLanguage,
        getCurrentLanguage,
        getAvailableLanguages,
        i18n: i18nInstance
    };
};
// Add new resources dynamically
export const addResources = (language, namespace, resources) => {
    i18n.addResourceBundle(language, namespace, resources, true, true);
};
// Get current language
export const getCurrentLanguage = () => i18n.language;
// Change language
export const changeLanguage = (language) => {
    return i18n.changeLanguage(language);
};
export default i18n;
