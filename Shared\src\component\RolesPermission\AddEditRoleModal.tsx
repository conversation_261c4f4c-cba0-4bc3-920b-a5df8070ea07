import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "../ui/dialog";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Save, X } from "lucide-react";
import { Permission } from "../../lib/permissionUtils";
import { useLanguage } from "../../i18n/LanguageProvider";
import PermissionSelector from "../PermissionSelector";

export interface AddEditRoleModalProps {
  open: boolean;
  onClose: () => void;
  role: {
    id?: string;
    name?: string;
    description?: string;
    permissionsList?: Permission[];
  };
  setRole: (role: any) => void;
  isEdit: boolean;
  onSave: () => void;
  availablePermissions: Permission[];
}

const AddEditRoleModal: React.FC<AddEditRoleModalProps> = ({
  open,
  onClose,
  role,
  setRole,
  isEdit,
  onSave,
  availablePermissions,
}) => {
  const { t } = useLanguage();

  const handlePermissionToggle = (permissionId: string) => {
    const currentPermissions = role.permissionsList || [];
    const exists = currentPermissions.some((p) => p._id === permissionId);

    let newPermissionsList;
    if (exists) {
      // Remove the permission object
      newPermissionsList = currentPermissions.filter(
        (p) => p._id !== permissionId
      );
    } else {
      // Add the permission object
      const permissionToAdd = availablePermissions.find(
        (p) => p._id === permissionId
      );
      if (permissionToAdd) {
        newPermissionsList = [...currentPermissions, permissionToAdd];
      } else {
        newPermissionsList = currentPermissions;
      }
    }

    setRole({ ...role, permissionsList: newPermissionsList });
  };

  const handleSave = () => {
    onSave();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className="p-0 gap-0 overflow-hidden"
        style={{
          maxWidth: "90vw",
          width: "70%",
          maxHeight: "90vh",
          height: "100%",
          minHeight: "600px",
        }}>
        <div
          style={{ display: "flex", flexDirection: "column", height: "100%" }}>
          <DialogHeader
            className="px-6 py-4 border-b"
            style={{ flexShrink: 0 }}>
            <DialogTitle className="text-lg font-semibold">
              {isEdit ? t("roles.edit") : t("roles.create")}
            </DialogTitle>
          </DialogHeader>

          <div
            className="px-6 py-4"
            style={{
              flex: 1,
              overflowY: "auto",
              //minHeight: 0,
              //maxHeight: 'calc(90vh - 140px)'
            }}>
            <div
              style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
              <div>
                <Label htmlFor="roleName" className="text-sm font-medium">
                  {t("roles.roleName")}
                </Label>
                <Input
                  id="roleName"
                  value={role.name || ""}
                  onChange={(e) => setRole({ ...role, name: e.target.value })}
                  placeholder={t("roles.roleNamePlaceholder")}
                  className="mt-1"
                />
              </div>

              <div>
                <Label
                  htmlFor="roleDescription"
                  className="text-sm font-medium">
                  {t("roles.description")}
                </Label>
                <Textarea
                  id="roleDescription"
                  value={role.description || ""}
                  onChange={(e) =>
                    setRole({ ...role, description: e.target.value })
                  }
                  placeholder={t("roles.descriptionPlaceholder")}
                  rows={2}
                  className="mt-1"
                />
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">
                  {t("roles.permissions")}
                </h3>
                <PermissionSelector
                  availablePermissions={availablePermissions}
                  selectedPermissions={role.permissionsList || []}
                  onTogglePermission={handlePermissionToggle}
                />
              </div>
            </div>
          </div>

          <DialogFooter
            className="px-6 py-4 border-t bg-background-50"
            style={{
              flexShrink: 0,
              position: "sticky",
              bottom: 0,
              zIndex: 10,
            }}>
            <div className="flex gap-3 w-full justify-end">
              <Button
                variant="outline"
                onClick={onClose}
                className="min-w-[100px]">
                <X className="w-4 h-4 mr-2" />
                {t("common.cancel")}
              </Button>
              <Button onClick={handleSave} className="min-w-[120px]">
                <Save className="w-4 h-4 mr-2" />
                {isEdit ? t("roles.update") : t("roles.create")}
              </Button>
            </div>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddEditRoleModal;
