import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Moon, Sun, Monitor, Check } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
const themeOptions = [
    {
        value: 'light',
        label: 'Light',
        icon: _jsx(Sun, { className: "h-4 w-4" }),
        description: 'Light theme for bright environments'
    },
    {
        value: 'dark',
        label: 'Dark',
        icon: _jsx(Moon, { className: "h-4 w-4" }),
        description: 'Dark theme for low-light environments'
    },
    {
        value: 'system',
        label: 'System',
        icon: _jsx(Monitor, { className: "h-4 w-4" }),
        description: 'Follow your system preferences'
    }
];
export function NextThemeToggle({ variant = 'select', size = 'default', showLabels = true, showDescriptions = false, className = '', useTheme, }) {
    const { theme, setTheme, resolvedTheme } = useTheme();
    const currentTheme = themeOptions.find(option => option.value === theme);
    const sizeClasses = {
        sm: 'h-8 text-sm',
        default: 'h-9 text-sm',
        lg: 'h-10 text-base',
    };
    // Icon-only toggle (cycles through themes)
    if (variant === 'icon') {
        const cycleTheme = () => {
            const themes = ['light', 'dark', 'system'];
            const currentIndex = themes.indexOf(theme || 'system');
            const nextIndex = (currentIndex + 1) % themes.length;
            setTheme(themes[nextIndex]);
        };
        return (_jsx(Button, { variant: "outline", size: size, onClick: cycleTheme, className: `${sizeClasses[size]} ${className}`, title: `Current: ${currentTheme?.label} - Click to cycle`, children: currentTheme?.icon }));
    }
    // Button group variant
    if (variant === 'buttons') {
        return (_jsx("div", { className: `flex gap-1 p-1 bg-muted rounded-lg ${className}`, children: themeOptions.map((option) => {
                const isSelected = option.value === theme;
                return (_jsx(Button, { variant: isSelected ? 'default' : 'ghost', size: size, onClick: () => setTheme(option.value), className: `${sizeClasses[size]} ${isSelected ? 'shadow-sm' : ''}`, title: option.description, children: _jsxs("div", { className: "flex items-center gap-2", children: [option.icon, showLabels && _jsx("span", { children: option.label })] }) }, option.value));
            }) }));
    }
    // Card variant for settings pages
    if (variant === 'cards') {
        return (_jsx("div", { className: `grid grid-cols-1 md:grid-cols-3 gap-3 ${className}`, children: themeOptions.map((option) => {
                const isSelected = option.value === theme;
                return (_jsx(Card, { className: `cursor-pointer transition-all duration-200 ${isSelected
                        ? 'ring-2 ring-primary border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'}`, onClick: () => setTheme(option.value), children: _jsx(CardContent, { className: "p-4", children: _jsxs("div", { className: "flex items-start justify-between mb-3", children: [_jsxs("div", { className: "flex-1", children: [_jsx("div", { className: "mb-2 text-gray-600", children: option.icon }), _jsx("h3", { className: "font-medium text-sm", children: option.label }), showDescriptions && (_jsx("p", { className: "text-xs text-gray-500 mt-1", children: option.description })), option.value === 'system' && resolvedTheme && (_jsxs(Badge, { variant: "outline", className: "text-xs mt-2", children: ["Currently: ", resolvedTheme] }))] }), isSelected && (_jsx("div", { className: "flex-shrink-0 ml-2", children: _jsx("div", { className: "w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center", children: _jsx(Check, { className: "w-3 h-3 text-white" }) }) }))] }) }) }, option.value));
            }) }));
    }
    // Default select variant
    return (_jsxs(Select, { value: theme, onValueChange: setTheme, children: [_jsx(SelectTrigger, { className: `w-40 ${sizeClasses[size]} bg-background border border-input hover:bg-accent hover:text-accent-foreground transition-colors ${className}`, children: _jsxs("div", { className: "flex items-center gap-2", children: [currentTheme?.icon, _jsx(SelectValue, { placeholder: "Select theme" })] }) }), _jsx(SelectContent, { align: "end", className: "w-48", children: themeOptions.map((option) => (_jsx(SelectItem, { value: option.value, className: "cursor-pointer", children: _jsxs("div", { className: "flex items-center gap-3 py-1", children: [_jsx("div", { className: "text-muted-foreground", children: option.icon }), _jsxs("div", { className: "flex flex-col", children: [_jsx("span", { className: "font-medium", children: option.label }), showDescriptions && (_jsx("span", { className: "text-xs text-muted-foreground", children: option.description })), option.value === 'system' && resolvedTheme && (_jsxs("span", { className: "text-xs text-muted-foreground", children: ["Currently: ", resolvedTheme] }))] })] }) }, option.value))) })] }));
}
// Pre-configured components that accept useTheme hook
export function NextThemeToggleButton({ useTheme }) {
    return _jsx(NextThemeToggle, { variant: "icon", useTheme: useTheme });
}
export function NextThemeSelector({ useTheme }) {
    return (_jsx(NextThemeToggle, { variant: "cards", showDescriptions: true, className: "max-w-2xl", useTheme: useTheme }));
}
export function NextThemeSwitcher({ useTheme }) {
    return (_jsx(NextThemeToggle, { variant: "buttons", size: "sm", showLabels: false, useTheme: useTheme }));
}
export default NextThemeToggle;
