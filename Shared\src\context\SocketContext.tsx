import React, { createContext, useContext, ReactNode } from 'react';
import { useAuthWithSocket } from '../hooks/useAuthWithSocket';

// Define the socket context type
interface SocketContextType {
  socket: ReturnType<typeof useAuthWithSocket>;
  logout: () => Promise<void>;
  isConnected: boolean;
  connectionError: string | null;
  emit: (event: string, data?: any) => void;
  on: (event: string, callback: (...args: any[]) => void) => void;
  off: (event: string, callback?: (...args: any[]) => void) => void;
}

// Create the context
const SocketContext = createContext<SocketContextType | null>(null);

// Props for the SocketProvider
interface SocketProviderProps {
  children: ReactNode;
  socketUrl: string;
  isAuthenticated: boolean;
  accessToken?: string;
  onAuthLogout: () => void | Promise<void>;
}

/**
 * Socket Provider Component
 * 
 * This component should be placed at the root of your React application
 * to provide socket functionality to all child components.
 * 
 * @example
 * ```typescript
 * // In your main App.tsx or index.tsx
 * import { SocketProvider } from './context/SocketContext';
 * 
 * const App = () => {
 *   const isAuthenticated = true; // from your auth state
 *   const accessToken = "your-jwt-token"; // from your auth state
 *   const socketUrl = "ws://localhost:8081"; // your socket server URL
 * 
 *   const handleAuthLogout = async () => {
 *     // Your logout logic
 *     localStorage.removeItem('token');
 *     window.location.href = '/login';
 *   };
 * 
 *   return (
 *     <SocketProvider
 *       socketUrl={socketUrl}
 *       isAuthenticated={isAuthenticated}
 *       accessToken={accessToken}
 *       onAuthLogout={handleAuthLogout}
 *     >
 *       <YourAppComponents />
 *     </SocketProvider>
 *   );
 * };
 * ```
 */
export const SocketProvider: React.FC<SocketProviderProps> = ({
  children,
  socketUrl,
  isAuthenticated,
  accessToken,
  onAuthLogout,
}) => {
  const socket = useAuthWithSocket({
    socketUrl,
    isAuthenticated,
    accessToken,
    onAuthLogout,
  });

  const contextValue: SocketContextType = {
    socket,
    logout: socket.logout,
    isConnected: socket.isConnected,
    connectionError: socket.connectionError,
    emit: socket.emit,
    on: socket.on,
    off: socket.off,
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

/**
 * Hook to use socket functionality in any component
 * 
 * This hook provides access to the socket instance and all its methods
 * from any component within the SocketProvider.
 * 
 * @returns {SocketContextType} Socket context with connection state and methods
 * @throws {Error} If used outside of SocketProvider
 * 
 * @example
 * ```typescript
 * import { useSocketContext } from './context/SocketContext';
 * 
 * const MyComponent = () => {
 *   const { logout, isConnected, emit, on, off } = useSocketContext();
 * 
 *   const handleLogout = async () => {
 *     try {
 *       await logout(); // This handles both socket and auth logout
 *       console.log('Successfully logged out');
 *     } catch (error) {
 *       console.error('Logout failed:', error);
 *     }
 *   };
 * 
 *   const sendMessage = () => {
 *     if (isConnected) {
 *       emit('message', { text: 'Hello from component' });
 *     }
 *   };
 * 
 *   useEffect(() => {
 *     if (isConnected) {
 *       on('notification', (data) => {
 *         console.log('Notification:', data);
 *       });
 *     }
 * 
 *     return () => {
 *       off('notification');
 *     };
 *   }, [isConnected, on, off]);
 * 
 *   return (
 *     <div>
 *       <p>Status: {isConnected ? 'Connected' : 'Disconnected'}</p>
 *       <button onClick={handleLogout}>Logout</button>
 *       <button onClick={sendMessage}>Send Message</button>
 *     </div>
 *   );
 * };
 * ```
 */
export const useSocketContext = (): SocketContextType => {
  const context = useContext(SocketContext);
  
  if (!context) {
    throw new Error('useSocketContext must be used within a SocketProvider');
  }
  
  return context;
};

// Export individual socket methods for convenience
export const useSocketLogout = () => {
  const { logout } = useSocketContext();
  return logout;
};

export const useSocketConnection = () => {
  const { isConnected, connectionError } = useSocketContext();
  return { isConnected, connectionError };
};

export const useSocketEmit = () => {
  const { emit } = useSocketContext();
  return emit;
};

export const useSocketEvents = () => {
  const { on, off } = useSocketContext();
  return { on, off };
};
