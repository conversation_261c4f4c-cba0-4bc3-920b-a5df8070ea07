import React from 'react';
import { useAuthWithSocket } from '../hooks/useAuthWithSocket';
import { useSocket } from '../hooks/useSocket';

// Example 1: Using useAuthWithSocket (recommended for most cases)
export const AuthWithSocketExample: React.FC = () => {
  // Your auth state (from your auth context/hook)
  const isAuthenticated = true; // Replace with actual auth state
  const accessToken = "your-jwt-token"; // Replace with actual token
  const socketUrl = "ws://localhost:8081"; // Pass from parent/config

  // Handle auth logout (your existing logout logic)
  const handleAuthLogout = async () => {
    console.log("Performing auth logout...");
    // Your existing logout logic here:
    // - Clear tokens
    // - Redirect to login
    // - Clear user state
    // etc.
  };

  const socket = useAuthWithSocket({
    socketUrl,
    isAuthenticated,
    accessToken,
    onAuthLogout: handleAuthLogout, // Called when server forces logout
  });

  const handleManualLogout = async () => {
    try {
      await socket.logout(); // This will do socket logout + auth logout
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <div>
      <h2>Auth with Socket Example</h2>
      <p>Socket Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
      {socket.connectionError && (
        <p style={{ color: 'red' }}>Error: {socket.connectionError}</p>
      )}
      <button onClick={handleManualLogout}>Logout</button>
    </div>
  );
};

// Example 2: Using useSocket directly (for more control)
export const DirectSocketExample: React.FC = () => {
  // Your auth state
  const isAuthenticated = true;
  const accessToken = "your-jwt-token";
  const socketUrl = "ws://localhost:8081";

  // Your logout handler
  const handleLogout = async () => {
    console.log("Handling logout from parent...");
    // Your logout logic here
  };

  const socket = useSocket({
    socketUrl,
    isAuthenticated,
    accessToken,
    onLogout: handleLogout, // Called when server forces logout
  });

  const handleManualLogout = async () => {
    try {
      // Only does socket logout - you handle auth logout separately
      await socket.logout();
      // Then handle your auth logout
      await handleLogout();
    } catch (error) {
      console.error("Socket logout failed:", error);
      // Still do auth logout even if socket logout fails
      await handleLogout();
    }
  };

  return (
    <div>
      <h2>Direct Socket Example</h2>
      <p>Socket Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
      <button onClick={handleManualLogout}>Logout</button>
      <button onClick={() => socket.emit('customEvent', { data: 'test' })}>
        Send Custom Event
      </button>
    </div>
  );
};

// Example 3: Integration with your existing auth context
interface AuthContextType {
  isAuthenticated: boolean;
  accessToken: string | null;
  logout: () => Promise<void>;
}

export const WithAuthContextExample: React.FC<{ 
  auth: AuthContextType,
  socketUrl: string 
}> = ({ auth, socketUrl }) => {
  const socket = useAuthWithSocket({
    socketUrl,
    isAuthenticated: auth.isAuthenticated,
    accessToken: auth.accessToken || undefined,
    onAuthLogout: auth.logout,
  });

  return (
    <div>
      <h2>With Auth Context Example</h2>
      <p>Auth Status: {auth.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>
      <p>Socket Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
      <button onClick={socket.logout}>Logout</button>
    </div>
  );
};
