interface NextThemeHook {
    theme: string | undefined;
    setTheme: (theme: string) => void;
    resolvedTheme: string | undefined;
    themes: string[];
    systemTheme: string | undefined;
}
interface AppearanceSettingsProps {
    showThemeSection?: boolean;
    showLanguageSection?: boolean;
    showCurrentStatus?: boolean;
    className?: string;
    title?: string;
    description?: string;
    useTheme?: () => NextThemeHook;
}
export declare function AppearanceSettings({ showThemeSection, showLanguageSection, showCurrentStatus, className, title, description, useTheme, }: AppearanceSettingsProps): import("react/jsx-runtime").JSX.Element;
export declare function CompactAppearanceSettings(): import("react/jsx-runtime").JSX.Element;
export declare function AppearanceDropdown(): import("react/jsx-runtime").JSX.Element;
export declare function AppearanceToolbar(): import("react/jsx-runtime").JSX.Element;
export default AppearanceSettings;
