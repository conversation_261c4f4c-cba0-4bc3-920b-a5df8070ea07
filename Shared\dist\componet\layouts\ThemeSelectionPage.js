import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Moon, Sun, Monitor } from 'lucide-react';
import SelectionPage from './SelectionPage';
const ThemeSelectionPage = ({ currentTheme, onThemeChange, onSave, onCancel, className = '', }) => {
    const themeOptions = [
        {
            id: 'light',
            name: 'Light',
            description: 'Light theme for bright environments',
            icon: _jsx(Sun, { className: "h-5 w-5" }),
            preview: (_jsxs("div", { className: "w-full h-24 bg-white border border-gray-200 rounded-md shadow-sm", children: [_jsx("div", { className: "h-6 bg-gray-100 border-b border-gray-200" }), _jsxs("div", { className: "p-2", children: [_jsx("div", { className: "w-1/2 h-3 bg-gray-200 rounded mb-2" }), _jsx("div", { className: "w-full h-2 bg-gray-100 rounded mb-1" }), _jsx("div", { className: "w-full h-2 bg-gray-100 rounded" })] })] })),
        },
        {
            id: 'dark',
            name: 'Dark',
            description: 'Dark theme for low-light environments',
            icon: _jsx(Moon, { className: "h-5 w-5" }),
            preview: (_jsxs("div", { className: "w-full h-24 bg-gray-900 border border-gray-700 rounded-md shadow-sm", children: [_jsx("div", { className: "h-6 bg-gray-800 border-b border-gray-700" }), _jsxs("div", { className: "p-2", children: [_jsx("div", { className: "w-1/2 h-3 bg-gray-700 rounded mb-2" }), _jsx("div", { className: "w-full h-2 bg-gray-800 rounded mb-1" }), _jsx("div", { className: "w-full h-2 bg-gray-800 rounded" })] })] })),
        },
        {
            id: 'system',
            name: 'System',
            description: 'Follow your system preferences',
            icon: _jsx(Monitor, { className: "h-5 w-5" }),
            preview: (_jsxs("div", { className: "w-full h-24 bg-gradient-to-r from-white to-gray-900 border border-gray-300 rounded-md shadow-sm", children: [_jsx("div", { className: "h-6 bg-gradient-to-r from-gray-100 to-gray-800 border-b border-gray-300" }), _jsxs("div", { className: "p-2 flex justify-between", children: [_jsxs("div", { className: "w-1/3", children: [_jsx("div", { className: "w-full h-3 bg-gray-200 rounded mb-2" }), _jsx("div", { className: "w-full h-2 bg-gray-100 rounded" })] }), _jsxs("div", { className: "w-1/3", children: [_jsx("div", { className: "w-full h-3 bg-gray-700 rounded mb-2" }), _jsx("div", { className: "w-full h-2 bg-gray-800 rounded" })] })] })] })),
        },
    ];
    const selectedItems = themeOptions.filter(option => option.id === currentTheme);
    const handleSelectionChange = (selected) => {
        if (selected.length > 0) {
            onThemeChange(selected[0].id);
        }
    };
    return (_jsx(SelectionPage, { title: "Select Theme", subtitle: "Choose the appearance for your application", items: themeOptions, selectedItems: selectedItems, onSelectionChange: handleSelectionChange, multiSelect: false, searchable: false, layout: "grid", columns: 3, showDescription: true, showPreview: true, onSave: onSave, onCancel: onCancel, saveButtonText: "Apply Theme", cancelButtonText: "Cancel", className: className }));
};
export default ThemeSelectionPage;
