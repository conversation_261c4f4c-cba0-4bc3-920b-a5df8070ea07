
import { Globe, Check } from 'lucide-react';
import { Button } from '../ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { useLanguage } from '../../i18n/LanguageProvider';

interface LanguageSelectorProps {
  variant?: 'select' | 'buttons' | 'cards' | 'icon';
  size?: 'sm' | 'default' | 'lg';
  showNativeNames?: boolean;
  showFlags?: boolean;
  showCodes?: boolean;
  className?: string;
  maxDisplayLanguages?: number;
}

export function LanguageSelector({
  variant = 'select',
  size = 'default',
  showNativeNames = true,
  showFlags = true,
  showCodes = false,
  className = '',
  maxDisplayLanguages,
}: LanguageSelectorProps) {
  const { language, setLanguage, languages, currentLanguage } = useLanguage();

  const displayLanguages = maxDisplayLanguages
    ? languages.slice(0, maxDisplayLanguages)
    : languages;

  const sizeClasses = {
    sm: 'h-8 text-sm',
    default: 'h-9 text-sm',
    lg: 'h-10 text-base',
  };

  // Icon-only toggle (cycles through first few languages)
  if (variant === 'icon') {
    const cycleLanguage = () => {
      const cycleLangs = displayLanguages.slice(0, 3); // Cycle through first 3
      const currentIndex = cycleLangs.findIndex(lang => lang.code === language);
      const nextIndex = (currentIndex + 1) % cycleLangs.length;
      setLanguage(cycleLangs[nextIndex].code);
    };

    return (
      <Button
        variant="outline"
        size={size}
        onClick={cycleLanguage}
        className={`${sizeClasses[size]} ${className}`}
        title={`Current: ${currentLanguage?.name} - Click to cycle`}
      >
        {showFlags && currentLanguage?.flag ? (
          <span className="text-base">{currentLanguage.flag}</span>
        ) : (
          <Globe className="h-4 w-4" />
        )}
      </Button>
    );
  }

  // Button group variant
  if (variant === 'buttons') {
    return (
      <div className={`flex gap-1 p-1 bg-muted rounded-lg ${className}`}>
        {displayLanguages.map((lang) => {
          const isSelected = lang.code === language;
          return (
            <Button
              key={lang.code}
              variant={isSelected ? 'default' : 'ghost'}
              size={size}
              onClick={() => setLanguage(lang.code)}
              className={`${sizeClasses[size]} ${isSelected ? 'shadow-sm' : ''}`}
              title={`${lang.name} (${lang.nativeName})`}
            >
              <div className="flex items-center gap-2">
                {showFlags && lang.flag && (
                  <span className="text-base">{lang.flag}</span>
                )}
                <span>
                  {showNativeNames ? lang.nativeName : lang.name}
                  {showCodes && ` (${lang.code})`}
                </span>
              </div>
            </Button>
          );
        })}
      </div>
    );
  }

  // Card variant for settings pages
  if (variant === 'cards') {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ${className}`}>
        {displayLanguages.map((lang) => {
          const isSelected = lang.code === language;
          return (
            <Card
              key={lang.code}
              className={`cursor-pointer transition-all duration-200 ${isSelected
                ? 'ring-2 ring-primary border-primary bg-primary/5'
                : 'border-border hover:border-primary/50'
                }`}
              onClick={() => setLanguage(lang.code)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    {showFlags && lang.flag && (
                      <span className="text-2xl">{lang.flag}</span>
                    )}
                    <div>
                      <div className="font-medium">{lang.name}</div>
                      {showNativeNames && lang.nativeName !== lang.name && (
                        <div className="text-sm text-muted-foreground">
                          {lang.nativeName}
                        </div>
                      )}
                    </div>
                  </div>
                  {isSelected && (
                    <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                      <Check className="w-2.5 h-2.5 text-primary-foreground" />
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {showCodes && (
                    <Badge variant="outline" className="text-xs">
                      {lang.code}
                    </Badge>
                  )}
                  {lang.rtl && (
                    <Badge variant="secondary" className="text-xs">
                      RTL
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  }

  // Default select variant
  return (
    <Select value={language} onValueChange={setLanguage}>
      <SelectTrigger style={{ width: '128px' }} className={`bg-background border border-input hover:bg-accent hover:text-accent-foreground transition-colors ${className}`}>
        <div className="flex items-center gap-2">
          {/* {showFlags && currentLanguage?.flag ? (
            <span className="text-base">{currentLanguage.flag}</span>
          ) : (
            <Globe className="h-4 w-4" />
          )} */}
          <SelectValue placeholder="Select language" />
        </div>
      </SelectTrigger>
      <SelectContent align="end" className="w-[128px]">
        {displayLanguages.map((lang) => (
          <SelectItem
            key={lang.code}
            value={lang.code}
            className="cursor-pointer"
          >
            <div className="flex items-center gap-3 py-1">
              {showFlags && lang.flag && (
                <span className="text-base">{lang.flag}</span>
              )}
              <div className="flex flex-col">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{lang.name}</span>
                  {showCodes && (
                    <Badge variant="outline" className="text-xs">
                      {lang.code}
                    </Badge>
                  )}
                </div>
                {showNativeNames && lang.nativeName !== lang.name && (
                  <span className="text-xs text-muted-foreground">
                    {lang.nativeName}
                  </span>
                )}
                {lang.rtl && (
                  <span className="text-xs text-muted-foreground">
                    Right-to-left
                  </span>
                )}
              </div>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

// Simplified language toggle button (just icon/flag)
export function LanguageToggleButton() {
  return <LanguageSelector variant="icon" />;
}

// Language selector for settings pages
export function LanguageSettings() {
  return (
    <LanguageSelector
      variant="cards"
      showNativeNames={true}
      showCodes={true}
      className="max-w-4xl"
    />
  );
}

// Compact language switcher for navbars
export function LanguageSwitcher() {
  return (
    <LanguageSelector
      variant="buttons"
      size="sm"
      maxDisplayLanguages={3}
      showNativeNames={false}
    />
  );
}

// Language dropdown with native names
export function LanguageDropdown() {
  return (
    <LanguageSelector
      variant="select"
      showNativeNames={true}
      showFlags={true}
    />
  );
}

export default LanguageSelector;
