import { type ThemeConfig } from './UniversalThemeProvider';
export declare const reactThemeConfig: ThemeConfig;
export declare const nextThemeConfig: ThemeConfig;
export declare const viteThemeConfig: ThemeConfig;
export declare const tailwindThemeConfig: ThemeConfig;
export declare const muiThemeConfig: ThemeConfig;
export declare const chakraThemeConfig: ThemeConfig;
export declare const antdThemeConfig: ThemeConfig;
export declare function createThemeConfig(overrides?: Partial<ThemeConfig>): ThemeConfig;
export declare const themeVariables: {
    light: {
        '--background': string;
        '--foreground': string;
        '--card': string;
        '--card-foreground': string;
        '--popover': string;
        '--popover-foreground': string;
        '--primary': string;
        '--primary-foreground': string;
        '--secondary': string;
        '--secondary-foreground': string;
        '--muted': string;
        '--muted-foreground': string;
        '--accent': string;
        '--accent-foreground': string;
        '--destructive': string;
        '--destructive-foreground': string;
        '--border': string;
        '--input': string;
        '--ring': string;
    };
    dark: {
        '--background': string;
        '--foreground': string;
        '--card': string;
        '--card-foreground': string;
        '--popover': string;
        '--popover-foreground': string;
        '--primary': string;
        '--primary-foreground': string;
        '--secondary': string;
        '--secondary-foreground': string;
        '--muted': string;
        '--muted-foreground': string;
        '--accent': string;
        '--accent-foreground': string;
        '--destructive': string;
        '--destructive-foreground': string;
        '--border': string;
        '--input': string;
        '--ring': string;
    };
};
export declare function injectThemeVariables(): void;
export declare const themeUtils: {
    prefersDark: () => boolean;
    prefersReducedMotion: () => boolean;
    getStoredTheme: (storageKey?: string) => string | null;
    setStoredTheme: (theme: string, storageKey?: string) => void;
    removeStoredTheme: (storageKey?: string) => void;
};
declare const _default: {
    reactThemeConfig: ThemeConfig;
    nextThemeConfig: ThemeConfig;
    viteThemeConfig: ThemeConfig;
    tailwindThemeConfig: ThemeConfig;
    muiThemeConfig: ThemeConfig;
    chakraThemeConfig: ThemeConfig;
    antdThemeConfig: ThemeConfig;
    createThemeConfig: typeof createThemeConfig;
    themeVariables: {
        light: {
            '--background': string;
            '--foreground': string;
            '--card': string;
            '--card-foreground': string;
            '--popover': string;
            '--popover-foreground': string;
            '--primary': string;
            '--primary-foreground': string;
            '--secondary': string;
            '--secondary-foreground': string;
            '--muted': string;
            '--muted-foreground': string;
            '--accent': string;
            '--accent-foreground': string;
            '--destructive': string;
            '--destructive-foreground': string;
            '--border': string;
            '--input': string;
            '--ring': string;
        };
        dark: {
            '--background': string;
            '--foreground': string;
            '--card': string;
            '--card-foreground': string;
            '--popover': string;
            '--popover-foreground': string;
            '--primary': string;
            '--primary-foreground': string;
            '--secondary': string;
            '--secondary-foreground': string;
            '--muted': string;
            '--muted-foreground': string;
            '--accent': string;
            '--accent-foreground': string;
            '--destructive': string;
            '--destructive-foreground': string;
            '--border': string;
            '--input': string;
            '--ring': string;
        };
    };
    injectThemeVariables: typeof injectThemeVariables;
    themeUtils: {
        prefersDark: () => boolean;
        prefersReducedMotion: () => boolean;
        getStoredTheme: (storageKey?: string) => string | null;
        setStoredTheme: (theme: string, storageKey?: string) => void;
        removeStoredTheme: (storageKey?: string) => void;
    };
};
export default _default;
