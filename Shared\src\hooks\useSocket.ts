import { useEffect, useRef, useState } from "react";
import io from "socket.io-client";
import { useAuth } from "@/hooks/useAuth";
import { useDecodedJwt } from "@/hooks/useDecodedJwt";

// Environment variables for Socket.IO configuration
const SOCKET_URL = import.meta.env.VITE_SOCKET || "http://localhost:8081";

// Socket connection options
const SOCKET_OPTIONS = {
  autoConnect: false,
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  upgrade: true,
  randomizationFactor: 0.5,
  transports: ["websocket", "polling"],
};

// Custom hook for Socket.IO connection management
export const useSocket = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const socketRef = useRef<any>(null);
  const isConnectingRef = useRef(false);
  const { isAuthenticated, accessToken } = useAuth();
  const jwtPayload = useDecodedJwt();

  // Create stable references for the dependency array
  const authStateRef = useRef({ isAuthenticated, accessToken });
  authStateRef.current = { isAuthenticated, accessToken };

  useEffect(() => {
    console.log("🔄 useSocket useEffect triggered", {
      isAuthenticated,
      hasAccessToken: !!accessToken,
      hasJwtPayload: !!jwtPayload,
      currentSocket: !!socketRef.current
    });

    // Only connect if user is authenticated and we have necessary data
    if (!isAuthenticated || !accessToken || !jwtPayload) {
      console.log("❌ Missing auth requirements, not connecting");
      return;
    }

    // If socket already exists and is connected, don't create a new one
    if (socketRef.current && socketRef.current.connected) {
      console.log("✅ Socket already connected, skipping creation");
      return;
    }

    // Prevent multiple simultaneous connections
    if (isConnectingRef.current) {
      console.log("⏳ Already connecting, skipping");
      return;
    }

    isConnectingRef.current = true;

    // Disconnect existing socket if any
    if (socketRef.current) {
      console.log("🔌 Disconnecting existing socket");
      socketRef.current.disconnect();
    }

    console.log("🚀 Creating new socket connection to:", SOCKET_URL);

    // Create socket connection
    const socket = io(SOCKET_URL, {
      ...SOCKET_OPTIONS,
      auth: {
        token: accessToken,
      },
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on("connect", () => {
      console.log("✅ Socket connected:", socket.id);
      isConnectingRef.current = false;
      setIsConnected(true);
      setConnectionError(null);
      debugger;
      // Join user-specific room after connection with unique socket ID format
      const userId = jwtPayload.userId;
      const uniqSocketID = jwtPayload.uniqSocketId;

      if (userId && uniqSocketID) {
        const uniqSocketId_userId = `${uniqSocketID}_${userId}`;
        socket.emit("join", { userId: uniqSocketId_userId });
        console.log("Joined socket room with uniqSocketId_userId:", uniqSocketId_userId);
      }
    });

    socket.on("disconnect", (reason: string) => {
      console.log("❌ Socket disconnected:", reason);
      isConnectingRef.current = false;
      setIsConnected(false);
    });

    socket.on("connect_error", (error: Error) => {
      console.error("❌ Socket connection error:", error);
      isConnectingRef.current = false;
      setConnectionError(error.message);
      setIsConnected(false);
    });

    socket.on("reconnect", (attemptNumber: number) => {
      console.log("Socket reconnected after", attemptNumber, "attempts");
      setIsConnected(true);
      setConnectionError(null);
    });

    socket.on("reconnect_error", (error: Error) => {
      console.error("Socket reconnection error:", error);
      setConnectionError(error.message);
    });

    // Listen for userLogout event from server
    socket.on("userLogout", (data: any) => {
      console.log("🔔 User logout event received:", data);
      // Handle forced logout from server
      if (data.message) {
        console.log("📢 Logout message:", data.message);
      }
      // You can add custom logout handling here
      // For example, redirect to login or show notification
    });

    // Connect the socket
    socket.connect();

    // Cleanup function
    return () => {
      console.log("🧹 Cleaning up socket connection");
      isConnectingRef.current = false;
      if (socket) {
        socket.disconnect();
      }
      socketRef.current = null;
      setIsConnected(false);
      setConnectionError(null);
    };
  }, [isAuthenticated, accessToken]); // Removed jwtPayload from dependencies to reduce re-renders

  // Helper functions
  const emit = (event: string, data?: any) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn("Socket not connected. Cannot emit event:", event);
    }
  };

  const on = (event: string, callback: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  };

  const off = (event: string, callback?: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.off(event, callback);
    }
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
  };

  const reconnect = () => {
    if (socketRef.current) {
      socketRef.current.connect();
    }
  };

  const logout = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!isConnected) {
        console.error('❌ Not connected to server');
        resolve(); // Don't reject, just resolve to continue with regular logout
        return;
      }

      if (socketRef.current) {
        socketRef.current.emit('logout', (response: any) => {
          if (response && response.success) {
            console.log('✅ Socket logged out successfully:', response.message);
            resolve();
          } else {
            console.error('❌ Socket logout failed:', response?.message || 'Unknown error');
            reject(new Error(response?.message || 'Socket logout failed'));
          }
        });
      } else {
        resolve(); // No socket, continue with regular logout
      }
    });
  };

  return {
    socket: socketRef.current,
    isConnected,
    connectionError,
    emit,
    on,
    off,
    disconnect,
    reconnect,
    logout,
  };
};

// Export default for backward compatibility
export default useSocket;
