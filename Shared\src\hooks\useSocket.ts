import { useEffect, useRef, useState } from "react";
import io from "socket.io-client";
import { useDecodedJwt } from "./useDecodedJwt";

// Socket connection options
const SOCKET_OPTIONS = {
  autoConnect: false,
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  upgrade: true,
  randomizationFactor: 0.5,
  transports: ["websocket", "polling"],
};

/**
 * Configuration options for the useSocket hook
 *
 * @interface UseSocketOptions
 * @property {string} [socketUrl="http://localhost:8081"] - The WebSocket server URL
 * @property {() => void | Promise<void>} [onLogout] - Callback function called when server forces logout
 * @property {boolean} [isAuthenticated=false] - Whether the user is currently authenticated
 * @property {string} [accessToken] - JWT access token for socket authentication
 *
 * @example
 * ```typescript
 * const options: UseSocketOptions = {
 *   socketUrl: "ws://localhost:8081",
 *   isAuthenticated: true,
 *   accessToken: "your-jwt-token",
 *   onLogout: async () => {
 *     console.log("Server forced logout");
 *     // Handle logout logic
 *   }
 * };
 * ```
 */
interface UseSocketOptions {
  socketUrl?: string;
  onLogout?: () => void | Promise<void>;
  isAuthenticated?: boolean;
  accessToken?: string;
}

/**
 * Custom hook for Socket.IO connection management
 *
 * This hook provides a comprehensive solution for managing WebSocket connections
 * with authentication, automatic reconnection, and event handling.
 *
 * Features:
 * - Automatic connection management based on authentication state
 * - JWT token-based authentication
 * - Automatic room joining with unique socket ID
 * - Reconnection handling with exponential backoff
 * - Server-forced logout handling
 * - Connection state management
 * - Error handling and logging
 *
 * @param {UseSocketOptions} [options={}] - Configuration options
 * @returns {Object} Socket instance with connection state and helper methods
 *
 * @example
 * Basic usage:
 * ```typescript
 * import { useSocket } from './hooks/useSocket';
 *
 * const MyComponent = () => {
 *   const socket = useSocket({
 *     socketUrl: "ws://localhost:8081",
 *     isAuthenticated: true,
 *     accessToken: "your-jwt-token",
 *     onLogout: () => console.log("Logged out by server")
 *   });
 *
 *   useEffect(() => {
 *     if (socket.isConnected) {
 *       socket.on('message', (data) => {
 *         console.log('Received:', data);
 *       });
 *     }
 *
 *     return () => {
 *       socket.off('message');
 *     };
 *   }, [socket.isConnected]);
 *
 *   return (
 *     <div>
 *       <p>Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
 *       <button onClick={() => socket.emit('ping', { timestamp: Date.now() })}>
 *         Send Ping
 *       </button>
 *     </div>
 *   );
 * };
 * ```
 *
 * @example
 * Handling connection errors:
 * ```typescript
 * const socket = useSocket({
 *   socketUrl: "ws://localhost:8081",
 *   isAuthenticated: true,
 *   accessToken: "your-token"
 * });
 *
 * if (socket.connectionError) {
 *   console.error('Connection failed:', socket.connectionError);
 * }
 * ```
 */
export const useSocket = (options: UseSocketOptions = {}) => {
  const {
    socketUrl = "http://localhost:8081",
    onLogout,
    isAuthenticated = false,
    accessToken
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const socketRef = useRef<any>(null);
  const isConnectingRef = useRef(false);
  const jwtPayload = useDecodedJwt();

  useEffect(() => {
    console.log("🔄 useSocket useEffect triggered", {
      isAuthenticated,
      hasAccessToken: !!accessToken,
      hasJwtPayload: !!jwtPayload,
      currentSocket: !!socketRef.current
    });

    // Only connect if user is authenticated and we have necessary data
    if (!isAuthenticated || !accessToken || !jwtPayload) {
      console.log("❌ Missing auth requirements, not connecting");
      return;
    }

    // If socket already exists and is connected, don't create a new one
    if (socketRef.current && socketRef.current.connected) {
      console.log("✅ Socket already connected, skipping creation");
      return;
    }

    // Prevent multiple simultaneous connections
    if (isConnectingRef.current) {
      console.log("⏳ Already connecting, skipping");
      return;
    }

    isConnectingRef.current = true;

    // Disconnect existing socket if any
    if (socketRef.current) {
      console.log("🔌 Disconnecting existing socket");
      socketRef.current.disconnect();
    }

    console.log("🚀 Creating new socket connection to:", socketUrl);

    // Create socket connection
    const socket = io(socketUrl, {
      ...SOCKET_OPTIONS,
      auth: {
        token: accessToken,
      },
    });

    socketRef.current = socket;

    // Connection event handlers
    socket.on("connect", () => {
      console.log("✅ Socket connected:", socket.id);
      isConnectingRef.current = false;
      setIsConnected(true);
      setConnectionError(null);
      debugger;
      // Join user-specific room after connection with unique socket ID format
      const userId = jwtPayload.userId;
      const uniqSocketID = jwtPayload.uniqSocketId;

      if (userId && uniqSocketID) {
        const uniqSocketId_userId = `${uniqSocketID}_${userId}`;
        socket.emit("join", { userId: uniqSocketId_userId });
        console.log("Joined socket room with uniqSocketId_userId:", uniqSocketId_userId);
      }
    });

    socket.on("disconnect", (reason: string) => {
      console.log("❌ Socket disconnected:", reason);
      isConnectingRef.current = false;
      setIsConnected(false);
    });

    socket.on("connect_error", (error: Error) => {
      console.error("❌ Socket connection error:", error);
      isConnectingRef.current = false;
      setConnectionError(error.message);
      setIsConnected(false);
    });

    socket.on("reconnect", (attemptNumber: number) => {
      console.log("Socket reconnected after", attemptNumber, "attempts");
      setIsConnected(true);
      setConnectionError(null);
    });

    socket.on("reconnect_error", (error: Error) => {
      console.error("Socket reconnection error:", error);
      setConnectionError(error.message);
    });

    // Listen for userLogout event from server
    socket.on("userLogout", async (data: any) => {
      console.log("🔔 User logout event received:", data);
      // Handle forced logout from server
      if (data.message) {
        console.log("📢 Logout message:", data.message);
      }

      // Call the parent logout callback if provided
      if (onLogout) {
        try {
          await onLogout();
        } catch (error) {
          console.error("❌ Parent logout callback failed:", error);
        }
      }
    });

    // Connect the socket
    socket.connect();

    // Cleanup function
    return () => {
      console.log("🧹 Cleaning up socket connection");
      isConnectingRef.current = false;
      if (socket) {
        socket.disconnect();
      }
      socketRef.current = null;
      setIsConnected(false);
      setConnectionError(null);
    };
  }, [isAuthenticated, accessToken, socketUrl]); // Dependencies for socket connection

  // Helper functions
  const emit = (event: string, data?: any) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn("Socket not connected. Cannot emit event:", event);
    }
  };

  const on = (event: string, callback: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback);
    }
  };

  const off = (event: string, callback?: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.off(event, callback);
    }
  };

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }
  };

  const reconnect = () => {
    if (socketRef.current) {
      socketRef.current.connect();
    }
  };

  const logout = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!isConnected) {
        console.error('❌ Not connected to server');
        resolve(); // Don't reject, just resolve to continue with regular logout
        return;
      }

      if (socketRef.current) {
        socketRef.current.emit('logout', (response: any) => {
          if (response && response.success) {
            console.log('✅ Socket logged out successfully:', response.message);
            resolve();
          } else {
            console.error('❌ Socket logout failed:', response?.message || 'Unknown error');
            reject(new Error(response?.message || 'Socket logout failed'));
          }
        });
      } else {
        resolve(); // No socket, continue with regular logout
      }
    });
  };

  return {
    socket: socketRef.current,
    isConnected,
    connectionError,
    emit,
    on,
    off,
    disconnect,
    reconnect,
    logout,
  };
};

// Export default for backward compatibility
export default useSocket;
