import { jsx as _jsx } from "react/jsx-runtime";
import { NextThemeToggle, NextThemeToggleButton, NextThemeSelector, NextThemeSwitcher } from './NextThemeToggle';
// Higher-order component factory
export function withNextThemes(Component, useTheme) {
    return function WrappedComponent(props) {
        return _jsx(Component, { ...props, useTheme: useTheme });
    };
}
// Factory function to create theme components with next-themes integration
export function createNextThemeComponents(useTheme) {
    return {
        ThemeToggle: (props) => (_jsx(NextThemeToggle, { ...props, useTheme: useTheme })),
        ThemeToggleButton: () => (_jsx(NextThemeToggleButton, { useTheme: useTheme })),
        ThemeSelector: () => (_jsx(NextThemeSelector, { useTheme: useTheme })),
        ThemeSwitcher: () => (_jsx(NextThemeSwitcher, { useTheme: useTheme })),
    };
}
// Pre-configured components (requires useTheme to be passed)
export const NextThemeComponents = {
    ThemeToggle: NextThemeToggle,
    ThemeToggleButton: NextThemeToggleButton,
    ThemeSelector: NextThemeSelector,
    ThemeSwitcher: NextThemeSwitcher,
};
export default withNextThemes;
