import React from 'react';
interface Person {
    id: string;
    name: string;
    email: string;
    organization: string;
    role: string;
    isUser: boolean;
    hasLogin: boolean;
    status: 'Active' | 'Inactive' | 'Pending';
}
interface EditPersonnelModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    person: Person | null;
    onUpdate: (person: Person) => void;
}
declare const EditPersonnelModal: React.FC<EditPersonnelModalProps>;
export default EditPersonnelModal;
