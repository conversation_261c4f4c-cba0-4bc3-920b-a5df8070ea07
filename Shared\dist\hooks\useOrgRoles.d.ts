import { Permission } from "../lib/permissionUtils";
export interface OrgRole {
    id: string;
    _id?: string;
    name: string;
    description: string;
    permissionsList: Permission[];
}
export interface CreateOrgRoleData {
    name: string;
    description: string;
    permissionsList: Permission[];
}
export interface UpdateOrgRoleData extends CreateOrgRoleData {
    id: string;
}
export declare const orgRolesApi: {
    getOrgRoles: (organizationId?: string) => Promise<any>;
    getOrgRolesSimple: (organizationId?: string) => Promise<any>;
    getOrgRole: (id: string, organizationId?: string) => Promise<OrgRole | null>;
    createOrgRole: (data: CreateOrgRoleData, organizationId?: string) => Promise<OrgRole>;
    updateOrgRole: (data: UpdateOrgRoleData, organizationId?: string) => Promise<OrgRole>;
    deleteOrgRole: (id: string, organizationId?: string) => Promise<void>;
};
export declare const orgRoleKeys: {
    all: readonly ["orgRoles"];
    lists: () => readonly ["orgRoles", "list"];
    list: () => readonly ["orgRoles", "list"];
    details: () => readonly ["orgRoles", "detail"];
    detail: (id: string) => readonly ["orgRoles", "detail", string];
};
export declare const useOrgRolesWithPagination: (organizationId?: string) => import("@tanstack/react-query").UseQueryResult<any, Error>;
export declare const useOrgRoles: (organizationId?: string) => import("@tanstack/react-query").UseQueryResult<any, Error>;
export declare const useOrgRole: (id: string, organizationId?: string) => import("@tanstack/react-query").UseQueryResult<OrgRole | null, Error>;
export declare const useCreateOrgRole: (organizationId?: string) => import("@tanstack/react-query").UseMutationResult<OrgRole, any, CreateOrgRoleData, unknown>;
export declare const useUpdateOrgRole: (organizationId?: string) => import("@tanstack/react-query").UseMutationResult<OrgRole, any, UpdateOrgRoleData, unknown>;
export declare const useDeleteOrgRole: (organizationId?: string) => import("@tanstack/react-query").UseMutationResult<void, any, string, unknown>;
