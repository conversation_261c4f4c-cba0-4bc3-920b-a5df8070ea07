# Using CommonComponent Theme Components with next-themes

This guide shows how to use CommonComponent theme components with the popular `next-themes` library.

## Installation

```bash
npm install next-themes @relativity/common-components
```

## Setup

### 1. Next.js App Router Setup

```tsx
// app/providers.tsx
'use client';

import { ThemeProvider } from 'next-themes';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {children}
    </ThemeProvider>
  );
}
```

```tsx
// app/layout.tsx
import { Providers } from './providers';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
```

### 2. Pages Router Setup

```tsx
// pages/_app.tsx
import { ThemeProvider } from 'next-themes';
import type { AppProps } from 'next/app';

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <Component {...pageProps} />
    </ThemeProvider>
  );
}
```

## Usage Methods

### Method 1: Direct Component Usage (Recommended)

```tsx
// components/ThemeToggle.tsx
'use client'; // For App Router

import { useTheme } from 'next-themes';
import { NextThemeToggle } from '@relativity/common-components';

export function ThemeToggle() {
  return (
    <NextThemeToggle 
      variant="select" 
      useTheme={useTheme}
    />
  );
}

export function ThemeToggleButton() {
  return (
    <NextThemeToggle 
      variant="icon" 
      useTheme={useTheme}
    />
  );
}

export function ThemeSelector() {
  return (
    <NextThemeToggle 
      variant="cards" 
      showDescriptions={true}
      useTheme={useTheme}
    />
  );
}
```

### Method 2: Using the Factory Function

```tsx
// lib/theme-components.tsx
'use client';

import { useTheme } from 'next-themes';
import { createNextThemeComponents } from '@relativity/common-components';

// Create all theme components with next-themes integration
export const {
  ThemeToggle,
  ThemeToggleButton,
  ThemeSelector,
  ThemeSwitcher,
} = createNextThemeComponents(useTheme);
```

Then use them anywhere:

```tsx
// components/Navbar.tsx
import { ThemeToggleButton } from '../lib/theme-components';

export function Navbar() {
  return (
    <nav className="flex justify-between items-center p-4">
      <h1>My App</h1>
      <ThemeToggleButton />
    </nav>
  );
}
```

### Method 3: Using Higher-Order Component

```tsx
// components/ThemeComponents.tsx
'use client';

import { useTheme } from 'next-themes';
import { withNextThemes, NextThemeToggle } from '@relativity/common-components';

// Create wrapped components
export const ThemeToggle = withNextThemes(NextThemeToggle, useTheme);
```

## Complete Examples

### Example 1: Navigation Bar

```tsx
// components/Header.tsx
'use client';

import { useTheme } from 'next-themes';
import { NextThemeToggle } from '@relativity/common-components';

export function Header() {
  return (
    <header className="border-b">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <h1 className="text-xl font-bold">My App</h1>
        
        <div className="flex items-center gap-3">
          {/* Icon only toggle */}
          <NextThemeToggle 
            variant="icon" 
            useTheme={useTheme}
          />
          
          {/* Or dropdown */}
          <NextThemeToggle 
            variant="select" 
            useTheme={useTheme}
          />
        </div>
      </div>
    </header>
  );
}
```

### Example 2: Settings Page

```tsx
// app/settings/page.tsx
'use client';

import { useTheme } from 'next-themes';
import { NextThemeToggle } from '@relativity/common-components';

export default function SettingsPage() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Settings</h1>
      
      <div className="space-y-6">
        <section>
          <h2 className="text-lg font-semibold mb-4">Appearance</h2>
          <NextThemeToggle 
            variant="cards"
            showDescriptions={true}
            useTheme={useTheme}
            className="max-w-2xl"
          />
        </section>
      </div>
    </div>
  );
}
```

### Example 3: Toolbar

```tsx
// components/Toolbar.tsx
'use client';

import { useTheme } from 'next-themes';
import { NextThemeToggle } from '@relativity/common-components';

export function Toolbar() {
  return (
    <div className="flex items-center gap-2 p-2 bg-muted rounded-lg">
      <NextThemeToggle 
        variant="buttons"
        size="sm"
        showLabels={false}
        useTheme={useTheme}
      />
    </div>
  );
}
```

### Example 4: Custom Hook Integration

```tsx
// hooks/useAppTheme.ts
'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export function useAppTheme() {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent hydration mismatch
  if (!mounted) {
    return {
      theme: undefined,
      setTheme,
      resolvedTheme: undefined,
      isDark: false,
      isLight: false,
    };
  }

  return {
    theme,
    setTheme,
    resolvedTheme,
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
  };
}

// Usage in component
function MyComponent() {
  const { isDark, setTheme } = useAppTheme();
  
  return (
    <div className={isDark ? 'dark-styles' : 'light-styles'}>
      <NextThemeToggle variant="select" useTheme={useTheme} />
    </div>
  );
}
```

## TypeScript Support

```tsx
// types/theme.ts
import type { NextThemeToggle } from '@relativity/common-components';

// Extract props type
type ThemeToggleProps = React.ComponentProps<typeof NextThemeToggle>;

// Create custom component with proper typing
interface CustomThemeToggleProps extends Omit<ThemeToggleProps, 'useTheme'> {
  customProp?: string;
}

export function CustomThemeToggle({ customProp, ...props }: CustomThemeToggleProps) {
  return <NextThemeToggle {...props} useTheme={useTheme} />;
}
```

## CSS Integration

```css
/* globals.css */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* ... other variables */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* ... other variables */
}

/* Use in your components */
.my-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
```

## Key Benefits

✅ **Seamless Integration** - Works perfectly with existing next-themes setup  
✅ **No Migration Needed** - Keep your current ThemeProvider  
✅ **SSR Safe** - Handles hydration properly  
✅ **TypeScript Support** - Full type safety  
✅ **Flexible** - Multiple component variants  
✅ **Consistent** - Same API as other CommonComponent themes  

## Migration from Built-in Theme Components

If you're already using next-themes and want to upgrade to CommonComponent UI:

```tsx
// Before
import { useTheme } from 'next-themes';

function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  return (
    <select value={theme} onChange={(e) => setTheme(e.target.value)}>
      <option value="light">Light</option>
      <option value="dark">Dark</option>
      <option value="system">System</option>
    </select>
  );
}

// After
import { useTheme } from 'next-themes';
import { NextThemeToggle } from '@relativity/common-components';

function ThemeToggle() {
  return <NextThemeToggle variant="select" useTheme={useTheme} />;
}
```

This approach gives you beautiful, accessible theme components while keeping your existing next-themes infrastructure!
