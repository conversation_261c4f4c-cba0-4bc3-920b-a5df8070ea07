/**
 * Configuration options for the useSocket hook
 *
 * @interface UseSocketOptions
 * @property {string} [socketUrl="http://localhost:8081"] - The WebSocket server URL
 * @property {() => void | Promise<void>} [onLogout] - Callback function called when server forces logout
 * @property {boolean} [isAuthenticated=false] - Whether the user is currently authenticated
 * @property {string} [accessToken] - JWT access token for socket authentication
 *
 * @example
 * ```typescript
 * const options: UseSocketOptions = {
 *   socketUrl: "ws://localhost:8081",
 *   isAuthenticated: true,
 *   accessToken: "your-jwt-token",
 *   onLogout: async () => {
 *     console.log("Server forced logout");
 *     // Handle logout logic
 *   }
 * };
 * ```
 */
interface UseSocketOptions {
    socketUrl?: string;
    onLogout?: () => void | Promise<void>;
    isAuthenticated?: boolean;
    accessToken?: string;
}
/**
 * Custom hook for Socket.IO connection management
 *
 * This hook provides a comprehensive solution for managing WebSocket connections
 * with authentication, automatic reconnection, and event handling.
 *
 * Features:
 * - Automatic connection management based on authentication state
 * - JWT token-based authentication
 * - Automatic room joining with unique socket ID
 * - Reconnection handling with exponential backoff
 * - Server-forced logout handling
 * - Connection state management
 * - Error handling and logging
 *
 * @param {UseSocketOptions} [options={}] - Configuration options
 * @returns {Object} Socket instance with connection state and helper methods
 *
 * @example
 * Basic usage:
 * ```typescript
 * import { useSocket } from './hooks/useSocket';
 *
 * const MyComponent = () => {
 *   const socket = useSocket({
 *     socketUrl: "ws://localhost:8081",
 *     isAuthenticated: true,
 *     accessToken: "your-jwt-token",
 *     onLogout: () => console.log("Logged out by server")
 *   });
 *
 *   useEffect(() => {
 *     if (socket.isConnected) {
 *       socket.on('message', (data) => {
 *         console.log('Received:', data);
 *       });
 *     }
 *
 *     return () => {
 *       socket.off('message');
 *     };
 *   }, [socket.isConnected]);
 *
 *   return (
 *     <div>
 *       <p>Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
 *       <button onClick={() => socket.emit('ping', { timestamp: Date.now() })}>
 *         Send Ping
 *       </button>
 *     </div>
 *   );
 * };
 * ```
 *
 * @example
 * Handling connection errors:
 * ```typescript
 * const socket = useSocket({
 *   socketUrl: "ws://localhost:8081",
 *   isAuthenticated: true,
 *   accessToken: "your-token"
 * });
 *
 * if (socket.connectionError) {
 *   console.error('Connection failed:', socket.connectionError);
 * }
 * ```
 */
export declare const useSocket: (options?: UseSocketOptions) => {
    socket: any;
    isConnected: boolean;
    connectionError: string | null;
    emit: (event: string, data?: any) => void;
    on: (event: string, callback: (...args: any[]) => void) => void;
    off: (event: string, callback?: (...args: any[]) => void) => void;
    disconnect: () => void;
    reconnect: () => void;
    logout: () => Promise<void>;
};
export default useSocket;
