import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

export interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag?: string;
  rtl?: boolean;
}

export interface LanguageConfig {
  defaultLanguage?: string;
  storageKey?: string;
  languages?: Language[];
  fallbackLanguage?: string;
  detectBrowserLanguage?: boolean;
}

export interface LanguageProviderState {
  language: string;
  setLanguage: (language: string) => void;
  languages: Language[];
  currentLanguage: Language | undefined;
  isRTL: boolean;
  t: (key: string, params?: Record<string, any>) => string;
}

interface LanguageProviderProps extends LanguageConfig {
  children: ReactNode;
  translations?: Record<string, Record<string, string>>;
  onLanguageChange?: (language: string) => void;
}

// Default language configurations
export const defaultLanguages: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
];

const defaultConfig: Required<LanguageConfig> = {
  defaultLanguage: 'en',
  storageKey: 'app-language',
  languages: defaultLanguages,
  fallbackLanguage: 'en',
  detectBrowserLanguage: true,
};

const LanguageProviderContext = createContext<LanguageProviderState | undefined>(undefined);

// Detect browser language
function getBrowserLanguage(availableLanguages: Language[]): string {
  if (typeof window === 'undefined') return 'en';

  const browserLanguages = navigator.languages || [navigator.language];

  for (const browserLang of browserLanguages) {
    // Check exact match
    const exactMatch = availableLanguages.find(lang => lang.code === browserLang);
    if (exactMatch) return exactMatch.code;

    // Check language prefix (e.g., 'en-US' -> 'en')
    const langPrefix = browserLang.split('-')[0];
    const prefixMatch = availableLanguages.find(lang => lang.code === langPrefix);
    if (prefixMatch) return prefixMatch.code;
  }

  return 'en';
}

// Simple translation function
function createTranslationFunction(
  translations: Record<string, Record<string, string>>,
  language: string,
  fallbackLanguage: string
) {
  return (key: string, params?: Record<string, any>): string => {
    const languageTranslations = translations[language] || {};
    const fallbackTranslations = translations[fallbackLanguage] || {};

    let translation = languageTranslations[key] || fallbackTranslations[key] || key;

    // Simple parameter replacement
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        translation = translation.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(value));
      });
    }

    return translation;
  };
}

export function LanguageProvider({
  children,
  defaultLanguage = defaultConfig.defaultLanguage,
  storageKey = defaultConfig.storageKey,
  languages = defaultConfig.languages,
  fallbackLanguage = defaultConfig.fallbackLanguage,
  detectBrowserLanguage = defaultConfig.detectBrowserLanguage,
  translations = {},
  onLanguageChange,
}: LanguageProviderProps) {
  const [language, setLanguageState] = useState<string>(() => {
    if (typeof window === 'undefined') return defaultLanguage;

    try {
      // Check stored language first
      const stored = localStorage.getItem(storageKey);
      if (stored && languages.some(lang => lang.code === stored)) {
        return stored;
      }

      // Detect browser language if enabled
      if (detectBrowserLanguage) {
        return getBrowserLanguage(languages);
      }

      return defaultLanguage;
    } catch {
      return defaultLanguage;
    }
  });

  const currentLanguage = languages.find(lang => lang.code === language);
  const isRTL = currentLanguage?.rtl || false;

  // Apply RTL/LTR direction to document
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
      document.documentElement.lang = language;
    }
  }, [language, isRTL]);

  const setLanguage = (newLanguage: string) => {
    if (!languages.some(lang => lang.code === newLanguage)) {
      console.warn(`Language "${newLanguage}" is not available`);
      return;
    }

    try {
      localStorage.setItem(storageKey, newLanguage);
    } catch {
      // Ignore localStorage errors
    }

    setLanguageState(newLanguage);
    onLanguageChange?.(newLanguage);
  };

  const t = createTranslationFunction(translations, language, fallbackLanguage);

  const contextValue: LanguageProviderState = {
    language,
    setLanguage,
    languages,
    currentLanguage,
    isRTL,
    t,
  };

  return (
    <LanguageProviderContext.Provider value={contextValue}>
      {children}
    </LanguageProviderContext.Provider>
  );
}

export const useLanguage = () => {
  const context = useContext(LanguageProviderContext);

  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }

  return context;
};

// Utility functions
export const languageUtils = {
  // Get stored language
  getStoredLanguage: (storageKey: string = 'app-language') => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem(storageKey);
    } catch {
      return null;
    }
  },

  // Set stored language
  setStoredLanguage: (language: string, storageKey: string = 'app-language') => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(storageKey, language);
    } catch {
      // Ignore localStorage errors
    }
  },

  // Detect browser language
  detectBrowserLanguage: (availableLanguages: Language[]) => {
    return getBrowserLanguage(availableLanguages);
  },

  // Get language by code
  getLanguageByCode: (code: string, languages: Language[]) => {
    return languages.find(lang => lang.code === code);
  },

  // Check if language is RTL
  isRTLLanguage: (code: string, languages: Language[]) => {
    const language = languages.find(lang => lang.code === code);
    return language?.rtl || false;
  },
};

export default LanguageProvider;
