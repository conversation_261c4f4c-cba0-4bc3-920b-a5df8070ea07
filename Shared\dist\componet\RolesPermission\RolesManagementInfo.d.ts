import React from "react";
export interface Role {
    id: string;
    _id?: string;
    name: string;
    description: string;
    permissionIds: string[];
}
export interface Permission {
    id: string;
    _id?: string;
    resource: string;
    action: string;
    description: string;
    createdAt?: string;
}
export interface RolesManagementInfoProps {
    roles: Role[];
    permissions: Permission[];
    isLoading?: boolean;
    isMutating?: boolean;
    onCreateRole: (role: Omit<Role, 'id'>) => void;
    onUpdateRole: (role: Role) => void;
    onDeleteRole: (roleId: string) => void;
}
declare const RolesManagementInfo: React.FC<RolesManagementInfoProps>;
export default RolesManagementInfo;
