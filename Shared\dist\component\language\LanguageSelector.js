import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Globe, Check } from 'lucide-react';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { useLanguage } from '../../i18n/LanguageProvider';
export function LanguageSelector({ variant = 'select', size = 'default', showNativeNames = true, showFlags = true, showCodes = false, className = '', maxDisplayLanguages, }) {
    const { language, setLanguage, languages, currentLanguage } = useLanguage();
    const displayLanguages = maxDisplayLanguages
        ? languages.slice(0, maxDisplayLanguages)
        : languages;
    const sizeClasses = {
        sm: 'h-8 text-sm',
        default: 'h-9 text-sm',
        lg: 'h-10 text-base',
    };
    // Icon-only toggle (cycles through first few languages)
    if (variant === 'icon') {
        const cycleLanguage = () => {
            const cycleLangs = displayLanguages.slice(0, 3); // Cycle through first 3
            const currentIndex = cycleLangs.findIndex(lang => lang.code === language);
            const nextIndex = (currentIndex + 1) % cycleLangs.length;
            setLanguage(cycleLangs[nextIndex].code);
        };
        return (_jsx(Button, { variant: "outline", size: size, onClick: cycleLanguage, className: `${sizeClasses[size]} ${className}`, title: `Current: ${currentLanguage?.name} - Click to cycle`, children: showFlags && currentLanguage?.flag ? (_jsx("span", { className: "text-base", children: currentLanguage.flag })) : (_jsx(Globe, { className: "h-4 w-4" })) }));
    }
    // Button group variant
    if (variant === 'buttons') {
        return (_jsx("div", { className: `flex gap-1 p-1 bg-muted rounded-lg ${className}`, children: displayLanguages.map((lang) => {
                const isSelected = lang.code === language;
                return (_jsx(Button, { variant: isSelected ? 'default' : 'ghost', size: size, onClick: () => setLanguage(lang.code), className: `${sizeClasses[size]} ${isSelected ? 'shadow-sm' : ''}`, title: `${lang.name} (${lang.nativeName})`, children: _jsxs("div", { className: "flex items-center gap-2", children: [showFlags && lang.flag && (_jsx("span", { className: "text-base", children: lang.flag })), _jsxs("span", { children: [showNativeNames ? lang.nativeName : lang.name, showCodes && ` (${lang.code})`] })] }) }, lang.code));
            }) }));
    }
    // Card variant for settings pages
    if (variant === 'cards') {
        return (_jsx("div", { className: `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ${className}`, children: displayLanguages.map((lang) => {
                const isSelected = lang.code === language;
                return (_jsx(Card, { className: `cursor-pointer transition-all duration-200 ${isSelected
                        ? 'ring-2 ring-primary border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'}`, onClick: () => setLanguage(lang.code), children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "flex items-center justify-between mb-2", children: [_jsxs("div", { className: "flex items-center gap-3", children: [showFlags && lang.flag && (_jsx("span", { className: "text-2xl", children: lang.flag })), _jsxs("div", { children: [_jsx("div", { className: "font-medium", children: lang.name }), showNativeNames && lang.nativeName !== lang.name && (_jsx("div", { className: "text-sm text-muted-foreground", children: lang.nativeName }))] })] }), isSelected && (_jsx("div", { className: "w-4 h-4 bg-primary rounded-full flex items-center justify-center", children: _jsx(Check, { className: "w-2.5 h-2.5 text-primary-foreground" }) }))] }), _jsxs("div", { className: "flex items-center gap-2", children: [showCodes && (_jsx(Badge, { variant: "outline", className: "text-xs", children: lang.code })), lang.rtl && (_jsx(Badge, { variant: "secondary", className: "text-xs", children: "RTL" }))] })] }) }, lang.code));
            }) }));
    }
    // Default select variant
    return (_jsxs(Select, { value: language, onValueChange: setLanguage, children: [_jsx(SelectTrigger, { style: { width: '128px' }, className: `bg-background border border-input hover:bg-accent hover:text-accent-foreground transition-colors ${className}`, children: _jsx("div", { className: "flex items-center gap-2", children: _jsx(SelectValue, { placeholder: "Select language" }) }) }), _jsx(SelectContent, { align: "end", className: "w-[128px]", children: displayLanguages.map((lang) => (_jsx(SelectItem, { value: lang.code, className: "cursor-pointer", children: _jsxs("div", { className: "flex items-center gap-3 py-1", children: [showFlags && lang.flag && (_jsx("span", { className: "text-base", children: lang.flag })), _jsxs("div", { className: "flex flex-col", children: [_jsxs("div", { className: "flex items-center gap-2", children: [_jsx("span", { className: "font-medium", children: lang.name }), showCodes && (_jsx(Badge, { variant: "outline", className: "text-xs", children: lang.code }))] }), showNativeNames && lang.nativeName !== lang.name && (_jsx("span", { className: "text-xs text-muted-foreground", children: lang.nativeName })), lang.rtl && (_jsx("span", { className: "text-xs text-muted-foreground", children: "Right-to-left" }))] })] }) }, lang.code))) })] }));
}
// Simplified language toggle button (just icon/flag)
export function LanguageToggleButton() {
    return _jsx(LanguageSelector, { variant: "icon" });
}
// Language selector for settings pages
export function LanguageSettings() {
    return (_jsx(LanguageSelector, { variant: "cards", showNativeNames: true, showCodes: true, className: "max-w-4xl" }));
}
// Compact language switcher for navbars
export function LanguageSwitcher() {
    return (_jsx(LanguageSelector, { variant: "buttons", size: "sm", maxDisplayLanguages: 3, showNativeNames: false }));
}
// Language dropdown with native names
export function LanguageDropdown() {
    return (_jsx(LanguageSelector, { variant: "select", showNativeNames: true, showFlags: true }));
}
export default LanguageSelector;
