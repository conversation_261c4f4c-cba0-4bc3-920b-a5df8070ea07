import React, { useState } from "react";
import { Plus, Trash2, Shield, Loader2, EditIcon } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import AddEditRoleModal from "./AddEditRoleModal";
import ConfirmDialog from "../confirmation/ConfirmDialog";
import {
  useRoles as useRolesManagement,
  useCreateRole,
  useUpdateRole,
  useDeleteRole,
  type Role,
  type CreateRoleData,
  type UpdateRoleData,
} from "../../hooks/useRoles";
import {
  useOrgPermissions,
  usePermissions as useRolePermissions,
  type Permission,
} from "../../hooks/useMaster";
import { ApplicationName } from "../../lib/Schemas/Personnel";
import { setApiUrl } from "../../lib/api";
import {
  useOrgRoles,
  useCreateOrgRole,
  useUpdateOrgRole,
  useDeleteOrgRole,
} from "../../hooks/useOrgRoles";
import { useLanguage } from "../../i18n/LanguageProvider";

export interface RolesManagementProps {
  appName: string;
  adminApiUrl: string;
  orgApiUrl?: string;
  orgId?: string;
  setRoles?: (rolesList: Role[]) => void;
  setPermissions?: (permissionsList: Permission[]) => void;
}

const RolesManagement: React.FC<RolesManagementProps> = ({
  appName,
  adminApiUrl,
  orgApiUrl,
  orgId,
  setRoles,
  setPermissions,
}) => {
  const { t } = useLanguage();
  // Set API URL based on appName and props
  React.useEffect(() => {
    const apiUrl =
      appName === ApplicationName.ORGANIZATION_ADMIN && orgApiUrl
        ? orgApiUrl
        : adminApiUrl;
    // You can set the API URL globally here if needed
    setApiUrl(apiUrl); // Uncomment if you want to use global API URL setting
  }, [appName, adminApiUrl, orgApiUrl, orgId]);

  // API hooks
  const {
    data: rolesData,
    isLoading: rolesLoading,
    error: rolesError,
  } = appName === ApplicationName.ORGANIZATION_ADMIN
      ? useOrgRoles(orgId)
      : useRolesManagement(appName);
  const { data: permissions = [], isLoading: permissionsLoading } =
    appName === ApplicationName.ORGANIZATION_ADMIN
      ? useOrgPermissions(adminApiUrl)
      : useRolePermissions(adminApiUrl);

  // Extract roles from the response data
  const roles = rolesData?.data || [];
  if (rolesData && setRoles) {
    setRoles(roles);
  }

  if (setPermissions && permissions?.length > 0) {
    setPermissions(permissions);
  }

  // Mutation hooks - use different hooks based on appName
  const createRoleMutation =
    appName === ApplicationName.ORGANIZATION_ADMIN
      ? useCreateOrgRole(orgId)
      : useCreateRole();
  const updateRoleMutation =
    appName === ApplicationName.ORGANIZATION_ADMIN
      ? useUpdateOrgRole(orgId)
      : useUpdateRole();
  const deleteRoleMutation =
    appName === ApplicationName.ORGANIZATION_ADMIN
      ? useDeleteOrgRole(orgId)
      : useDeleteRole();

  // Loading and mutation states
  const isLoading = rolesLoading || permissionsLoading;
  const isMutating =
    createRoleMutation.isPending ||
    updateRoleMutation.isPending ||
    deleteRoleMutation.isPending;

  // Local state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [currentRole, setCurrentRole] = useState<Partial<Role>>({
    name: "",
    description: "",
    permissionsList: [],
  });
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<string | null>(null);

  // Event handlers
  const openCreateDialog = () => {
    setCurrentRole({
      name: "",
      description: "",
      permissionsList: [],
    });
    setIsEdit(false);
    setDialogOpen(true);
  };

  const openEditDialog = (role: Role) => {
    setCurrentRole({
      _id: role._id,
      name: role.name,
      description: role.description,
      permissionsList: Array.isArray(role.permissionsList)
        ? role.permissionsList
        : [],
    });
    setIsEdit(true);
    setDialogOpen(true);
  };

  const handleSave = () => {
    if (isEdit && currentRole._id) {
      const updateData: UpdateRoleData = {
        id: currentRole._id,
        name: currentRole.name || "",
        description: currentRole.description || "",
        permissionsList: currentRole.permissionsList || [],
      };
      updateRoleMutation.mutate(updateData);
    } else {
      const createData: CreateRoleData = {
        name: currentRole.name || "",
        description: currentRole.description || "",
        permissionsList: currentRole.permissionsList || [],
      };
      createRoleMutation.mutate(createData);
    }
    setDialogOpen(false);
  };

  const handleDelete = (id: string) => {
    setRoleToDelete(id);
    setConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    if (roleToDelete) {
      deleteRoleMutation.mutate(roleToDelete);
      setConfirmOpen(false);
      setRoleToDelete(null);
    }
  };

  // Error handling
  if (rolesError) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-destructive mb-2">
                {t("roles.error")}
              </h3>
              <p className="text-sm text-muted-foreground">
                {rolesError.message ||
                  "Failed to load roles. Please try again."}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-2xl font-bold">
              {t("roles.title")}
            </CardTitle>
            <Button
              onClick={openCreateDialog}
              disabled={isMutating}
              className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              {t("roles.create")}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading || isMutating ? (
            <div className="flex justify-center items-center min-h-[200px]">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>{t("roles.loading")}</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {roles.map((role: Role) => (
                <Card key={role._id} className="border">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex gap-3 items-center">
                        <div className="bg-blue-100 p-2 rounded-lg">
                          <Shield className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{role.name}</h3>
                          <p className="text-sm text-muted-foreground mb-2">
                            {role.description}
                          </p>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {(role.permissionsList || []).map((permission) => (
                              <Badge
                                key={permission._id}
                                variant="secondary"
                                className="text-xs">
                                {permission.description}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openEditDialog(role)}
                          disabled={isMutating}>
                          <EditIcon className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(role._id || "")}
                          disabled={isMutating}
                          className="text-destructive hover:text-destructive-foreground">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <AddEditRoleModal
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        role={currentRole}
        setRole={setCurrentRole}
        isEdit={isEdit}
        onSave={handleSave}
        availablePermissions={permissions as Permission[]}
      />

      <ConfirmDialog
        open={confirmOpen}
        onCancel={() => setConfirmOpen(false)}
        onConfirm={handleConfirmDelete}
        title={t("roles.delete")}
        description={t("roles.deleteMessage")}
        confirmText={t("roles.deleteConfirm")}
        cancelText={t("common.cancel")}
      />
    </div>
  );
};

export default RolesManagement;
