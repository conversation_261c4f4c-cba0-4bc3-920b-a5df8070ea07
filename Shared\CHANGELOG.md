# Changelog

All notable changes to the UI Common package will be documented in this file.

## [1.0.0] - 2024-06-13

### Added
- Initial release of @shared/ui-common package
- Theme management with light, dark, and system theme support
- Internationalization (i18n) with English, Spanish, and French translations
- React context providers for theme and i18n
- TypeScript support with full type definitions
- Automatic language detection and localStorage persistence
- Common UI translations for buttons, navigation, status indicators
- Utility functions for theme management and resource merging
- Comprehensive documentation and examples
- Build scripts and development workflow

### Features
- **ThemeProvider**: Manages application theme state with system preference detection
- **useTheme**: React hook for theme management
- **initI18n**: Configurable i18n initialization with resource merging
- **useI18n**: React hook for internationalization
- **Default Resources**: Pre-built translations for common UI elements
- **Type Safety**: Full TypeScript support for better development experience

### Supported Languages
- English (en) - Default
- Spanish (es)
- French (fr)

### Documentation
- README with usage examples
- Multi-application setup guide
- Basic implementation examples
- API reference documentation