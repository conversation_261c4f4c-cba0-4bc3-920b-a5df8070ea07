import { useSocket } from "./useSocket";
/**
 * Custom hook that combines authentication and socket functionality
 *
 * This hook provides an integrated solution for managing WebSocket connections
 * with authentication. It handles both socket logout and authentication logout
 * in a coordinated manner.
 *
 * @param {UseAuthWithSocketOptions} [options={}] - Configuration options
 * @returns {Object} Socket instance with integrated logout functionality
 *
 * @example
 * Basic usage:
 * ```typescript
 * import { useAuthWithSocket } from './hooks/useAuthWithSocket';
 *
 * const MyComponent = () => {
 *   const socket = useAuthWithSocket({
 *     socketUrl: "ws://localhost:8081",
 *     isAuthenticated: true,
 *     accessToken: "your-jwt-token",
 *     onAuthLogout: async () => {
 *       // Your logout logic here
 *       console.log("Logging out...");
 *     }
 *   });
 *
 *   return (
 *     <div>
 *       <p>Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
 *       <button onClick={socket.logout}>Logout</button>
 *     </div>
 *   );
 * };
 * ```
 *
 * @example
 * With auth context:
 * ```typescript
 * import { useContext } from 'react';
 * import { AuthContext } from './AuthContext';
 * import { useAuthWithSocket } from './hooks/useAuthWithSocket';
 *
 * const MyComponent = () => {
 *   const { isAuthenticated, accessToken, logout } = useContext(AuthContext);
 *
 *   const socket = useAuthWithSocket({
 *     socketUrl: process.env.REACT_APP_SOCKET_URL,
 *     isAuthenticated,
 *     accessToken,
 *     onAuthLogout: logout
 *   });
 *
 *   const handleLogout = async () => {
 *     try {
 *       await socket.logout(); // Handles both socket and auth logout
 *     } catch (error) {
 *       console.error('Logout failed:', error);
 *     }
 *   };
 *
 *   return (
 *     <div>
 *       <button onClick={handleLogout}>Logout</button>
 *       <button onClick={() => socket.emit('message', { text: 'Hello' })}>
 *         Send Message
 *       </button>
 *     </div>
 *   );
 * };
 * ```
 *
 * @example
 * Handling server-forced logout:
 * ```typescript
 * const socket = useAuthWithSocket({
 *   socketUrl: "ws://localhost:8081",
 *   isAuthenticated: true,
 *   accessToken: "your-token",
 *   onAuthLogout: async () => {
 *     // This will be called automatically when server sends 'userLogout' event
 *     alert('You have been logged out by the server');
 *     // Clear local storage
 *     localStorage.clear();
 *     // Redirect to login
 *     window.location.href = '/login';
 *   }
 * });
 * ```
 */
export const useAuthWithSocket = (options = {}) => {
    const { socketUrl, isAuthenticated, accessToken, onAuthLogout } = options;
    const socket = useSocket({
        socketUrl,
        isAuthenticated,
        accessToken,
        onLogout: onAuthLogout, // This will be called when server forces logout
    });
    /**
     * Integrated logout function that handles both socket and authentication logout
     *
     * This function performs logout in the following order:
     * 1. Attempts socket logout (emits 'logout' event to server)
     * 2. Calls the provided onAuthLogout callback
     *
     * If socket logout fails, it will still proceed with auth logout.
     * If auth logout fails, it will throw an error.
     *
     * @throws {Error} If authentication logout fails
     *
     * @example
     * ```typescript
     * const handleLogout = async () => {
     *   try {
     *     await socket.logout();
     *     console.log('Successfully logged out');
     *   } catch (error) {
     *     console.error('Logout failed:', error);
     *   }
     * };
     * ```
     */
    const logout = async () => {
        try {
            // First, perform socket logout if connected
            if (socket.isConnected && socket.logout) {
                console.log("🔌 Performing socket logout...");
                await socket.logout();
            }
        }
        catch (error) {
            console.error("❌ Socket logout failed:", error);
            // Continue with regular logout even if socket logout fails
        }
        // Then perform regular auth logout
        try {
            if (onAuthLogout) {
                await onAuthLogout();
            }
        }
        catch (error) {
            console.error("❌ Auth logout failed:", error);
            throw error;
        }
    };
    return {
        ...socket,
        logout, // Override the socket logout with integrated logout
    };
};
export default useAuthWithSocket;
