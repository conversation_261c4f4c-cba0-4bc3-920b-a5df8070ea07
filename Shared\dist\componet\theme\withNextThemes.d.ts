import React from 'react';
import { NextThemeToggle, NextThemeToggleButton, NextThemeSelector, NextThemeSwitcher } from './NextThemeToggle';
interface NextThemeHook {
    theme: string | undefined;
    setTheme: (theme: string) => void;
    resolvedTheme: string | undefined;
    themes: string[];
    systemTheme: string | undefined;
}
export declare function withNextThemes<P extends object>(Component: React.ComponentType<P & {
    useTheme: () => NextThemeHook;
}>, useTheme: () => NextThemeHook): (props: P) => import("react/jsx-runtime").JSX.Element;
export declare function createNextThemeComponents(useTheme: () => NextThemeHook): {
    ThemeToggle: (props: Omit<React.ComponentProps<typeof NextThemeToggle>, "useTheme">) => import("react/jsx-runtime").JSX.Element;
    ThemeToggleButton: () => import("react/jsx-runtime").JSX.Element;
    ThemeSelector: () => import("react/jsx-runtime").JSX.Element;
    ThemeSwitcher: () => import("react/jsx-runtime").JSX.Element;
};
export declare const NextThemeComponents: {
    ThemeToggle: typeof NextThemeToggle;
    ThemeToggleButton: typeof NextThemeToggleButton;
    ThemeSelector: typeof NextThemeSelector;
    ThemeSwitcher: typeof NextThemeSwitcher;
};
export default withNextThemes;
