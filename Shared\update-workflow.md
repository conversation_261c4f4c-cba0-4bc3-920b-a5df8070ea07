# CommonComponent Library - Usage & Update Workflow

This guide explains how to use the `@relativity/common-components` library in another React project and manage updates efficiently.

## 🚀 Quick Start - Using in Another React Project

### Method 1: Local Development with npm link (Recommended for Development)

#### Step 1: Set up the library for linking
```bash
# In CommonComponent directory
npm run build
npm link
```

#### Step 2: Link in your React project
```bash
# In your React project directory
npm link @relativity/common-components
```

#### Step 3: Install peer dependencies in your React project
```bash
# In your React project (if not already installed)
npm install react@^18.3.1 react-dom@^18.3.1
```

#### Step 4: API Configuration (Two Options)

**✅ Option 1: Pass API URLs as Props (Recommended)**

Configure API URLs from your parent React app:

```jsx
// In your React app
import { PersonnelManagementInfo, ApiConfig } from '@relativity/common-components';

const apiConfig: ApiConfig = {
  development: "https://cloud-api-admin-dev.rpsmobile.com",
  local: "http://localhost:5007"
};

function MyApp() {
  return (
    <PersonnelManagementInfo
      appName="MyApp"
      selectedOrganizationId={orgId}
      onOrganizationChange={handleOrgChange}
      apiConfig={apiConfig} // Pass API config as props
    />
  );
}
```

**✅ Option 2: Automatic Detection (Fallback)**

If no `apiConfig` prop is provided, the library automatically detects:
- **🏠 Local**: `http://localhost:5007` (when on localhost)
- **🌐 Development**: `https://cloud-api-admin-dev.rpsmobile.com` (other cases)

#### Step 5: Use components in your React project
```jsx
// In your React component
import {
  Button,
  Card,
  Input,
  PersonnelManagementInfo,
  AddPersonnelModal,
  api,
  usePersonnel
} from '@relativity/common-components';

function MyComponent() {
  // Use the API client that automatically uses the correct environment
  const { data: personnel } = usePersonnel();

  return (
    <div>
      <Card>
        <Input placeholder="Enter text" />
        <Button>Click me</Button>
        <PersonnelManagementInfo />
      </Card>
    </div>
  );
}

// Or use the API client directly
import { api } from '@relativity/common-components';

async function fetchData() {
  try {
    const response = await api.get('/personnel');
    console.log('Personnel data:', response.data);
  } catch (error) {
    console.error('API Error:', error);
  }
}
```

### Method 2: File Path Installation (Alternative)

```bash
# In your React project directory
npm install file:../path/to/CommonComponent
```

### Method 3: Git Repository Installation

```bash
# If your library is in a git repository
npm install git+https://github.com/your-org/relativity-common-components.git
```

## 🔄 Development Workflow & Updates

### 1. Making Changes to CommonComponent

1. **Edit components** in `src/` directory (like the new role-based features)
2. **Test changes** locally
3. **Build the library**:
   ```bash
   npm run build
   ```

### 2. 🚀 **NEW PROCESS** - Update Your React Project Locally

#### **Method 1: Using npm link (Automatic Updates)**

**Initial Setup (do once):**
```bash
# In CommonComponent directory
npm run build
npm link

# In your React project directory
npm link @relativity/common-components
```

**After Making Changes (every time you update CommonComponent):**
```bash
# In CommonComponent directory - after making changes
npm run build
# Your React project automatically gets the updates!
```

#### **Method 2: Using File Installation (Manual Updates)**

**Initial Setup:**
```bash
# In your React project directory
npm install "file:../CommonComponent"
```

**After Making Changes (every time you update CommonComponent):**
```bash
# Step 1: Build CommonComponent
cd "path/to/CommonComponent"
npm run build

# Step 2: Update in your React project
cd "path/to/your/react/project"
npm install "file:../CommonComponent" --force
# The --force flag ensures it reinstalls even if version hasn't changed
```

#### **Method 3: Quick Update Script (Recommended)**

Create this script in your React project root as `update-common-components.bat`:

```batch
@echo off
echo Updating CommonComponent library...
cd "..\CommonComponent"
call npm run build
cd "..\YourReactProject"
call npm install "file:../CommonComponent" --force
echo CommonComponent updated successfully!
pause
```

Then just run:
```bash
# In your React project directory
update-common-components.bat
```

### 3. 🔍 **Verify Updates Work**

After updating, verify the new features work:

```jsx
// Test the new role-based functionality
import { AddPersonnelModal, useDecodedJwt } from '@relativity/common-components';

function TestComponent() {
  const jwtPayload = useDecodedJwt();

  // Check if new role-based logic is working
  console.log('Current user role:', jwtPayload?.role?.name);

  return (
    <AddPersonnelModal
      open={true}
      onOpenChange={() => {}}
      mode="add"
      // Should now show disabled org dropdown for "Organization Admin"
    />
  );
}
```

### 4. Version Management

```bash
# Patch version (1.1.0 -> 1.1.1) - for bug fixes
npm version patch

# Minor version (1.1.0 -> 1.2.0) - for new features
npm version minor

# Major version (1.1.0 -> 2.0.0) - for breaking changes
npm version major
```

**📦 Current Version: 1.1.0**
- ✅ Role-based AddPersonnelModal logic
- ✅ JWT token decoding with real implementation
- ✅ Organization Admin restrictions
- ✅ Super Admin role filtering
- ✅ Automatic API environment detection

### 5. 📋 **Complete Update Checklist**

When you make changes to CommonComponent:

- [ ] **Edit** your components in `src/`
- [ ] **Build** the library: `npm run build`
- [ ] **Update** your React project using one of the methods above
- [ ] **Test** the new functionality in your React project
- [ ] **Verify** role-based features work correctly
- [ ] **Check** console for any errors

### 6. 🐛 **Troubleshooting Updates**

If updates don't seem to work:

```bash
# Clear npm cache
npm cache clean --force

# Force reinstall in React project
npm uninstall @relativity/common-components
npm install "file:../CommonComponent" --force

# Or if using npm link, re-link
npm unlink @relativity/common-components
cd "../CommonComponent"
npm link
cd "../YourReactProject"
npm link @relativity/common-components
```

## 🛠️ Troubleshooting

### npm link Not Working? Try These Solutions:

#### Solution 1: Check if link was created properly
```bash
# In CommonComponent directory - verify the link was created
npm link
# Should show: "up to date, audited X packages"

# Check global links
npm list -g --depth=0
# Should show @relativity/common-components in the list
```

#### Solution 2: Use full path for linking
```bash
# In your React project directory
npm link "d:\Rheal Software\Projects\Relativity Cloud\cloud-rpsmobile-new\CommonComponent"
```

#### Solution 3: Alternative - Direct File Installation (Recommended if npm link fails)
```bash
# In your React project directory
npm install "file:../CommonComponent"
# or with full path
npm install "file:d:/Rheal Software/Projects/Relativity Cloud/cloud-rpsmobile-new/CommonComponent"
```

#### Solution 4: Manual Symlink (Windows)
```bash
# In your React project's node_modules directory
# Create symbolic link manually
mklink /D "@relativity" "d:\Rheal Software\Projects\Relativity Cloud\cloud-rpsmobile-new\CommonComponent\node_modules\@relativity"
```

### Common Issues and Solutions

1. **npm link command not recognized**:
   ```bash
   # Make sure you're in the correct directory
   # In CommonComponent directory first:
   cd "d:\Rheal Software\Projects\Relativity Cloud\cloud-rpsmobile-new\CommonComponent"
   npm run build
   npm link

   # Then in your React project:
   cd "path\to\your\react\project"
   npm link @relativity/common-components
   ```

2. **Permission Issues (Windows)**:
   ```bash
   # Run PowerShell as Administrator and try:
   npm link @relativity/common-components
   ```

3. **Package not found after linking**:
   ```bash
   # Check if the package name matches exactly
   # In CommonComponent/package.json, the name is: "@relativity/common-components"
   # Use exactly this name when linking
   ```

4. **React Hook Errors** (when using npm link):
   ```bash
   # In your React project, link React back to avoid duplicate React instances
   cd node_modules/react
   npm link
   cd ../../../CommonComponent
   npm link react
   ```

5. **TypeScript Errors**:
   - Ensure your React project has compatible TypeScript version
   - Check that `@types/react` versions match

6. **CSS/Styling Issues**:
   - Make sure to import CSS files if your components use them
   - Check Tailwind CSS configuration if using Tailwind

### Verification Steps

After installation, verify it worked:

```bash
# In your React project
npm list @relativity/common-components
# Should show the package and version

# Check if you can import (create a test file)
# test-import.js
try {
  const components = require('@relativity/common-components');
  console.log('Import successful:', Object.keys(components));
} catch (error) {
  console.error('Import failed:', error.message);
}
```

### Unlinking (when done with development)

```bash
# In your React project
npm unlink @relativity/common-components

# In CommonComponent directory
npm unlink

# Or if using file installation
npm uninstall @relativity/common-components
```

## 📦 Production Deployment Options

### Option A: NPM Registry (Recommended for Production)
```bash
# In CommonComponent directory
npm run build
npm publish
```

Then in React projects:
```bash
npm install @relativity/common-components
```

### Option B: Private Git Repository
```bash
# In CommonComponent directory
git add .
git commit -m "Release v1.0.2"
git tag v1.0.2
git push origin main --tags
```

Then in React projects:
```bash
npm install git+https://github.com/your-org/relativity-common-components.git#v1.0.2
```

## 🌐 API Configuration

The CommonComponent library includes a **self-contained** API client that automatically detects your environment - **no setup required!**

### ✅ Automatic Environment Detection (Built-in):
- **🏠 Local Development**: `http://localhost:5007` (when running on localhost)
- **🌐 Development Environment**: `https://cloud-api-admin-dev.rpsmobile.com` (for all other cases)

### 🎯 How It Works:
```javascript
// Built into CommonComponent/src/lib/api.ts
const API_ENDPOINTS = {
  development: "https://cloud-api-admin-dev.rpsmobile.com",
  local: "http://localhost:5007"
};

// Automatic detection:
if (window.location.hostname === 'localhost') {
  // Uses local API
} else {
  // Uses development API
}
```

### 🚀 Zero Configuration Required:
- **No .env files needed**
- **No environment variables to set**
- **No manual configuration**
- **Works out of the box**

### 🔍 Debug API Configuration:
You can check which API URL is being used:
```javascript
import { apiConfig } from '@relativity/common-components';

console.log('Current API URL:', apiConfig.currentUrl);
console.log('Available endpoints:', apiConfig.endpoints);
console.log('Detected environment:', apiConfig.environment);
```

## 🔐 Role-Based Features

The CommonComponent library includes intelligent role-based functionality:

### **JWT Token Decoding:**
- Automatically decodes JWT tokens from localStorage
- Extracts user role and organization information
- Supports role-based UI behavior

### **AddPersonnelModal Role-Based Logic:**

#### **For "Organization Admin" Users:**
- **Organization Dropdown**: Automatically disabled and pre-selected with user's organization
- **Visual Indicator**: Shows "(Auto-selected for your role)" message
- **Prevents Cross-Organization Access**: Users can only add personnel to their own organization

#### **For All Users:**
- **Super Admin Role**: Automatically removed from role dropdown for security
- **Role Filtering**: Only shows appropriate roles based on user permissions

### **Usage Example:**
```jsx
import { AddPersonnelModal, useDecodedJwt } from '@relativity/common-components';

function MyComponent() {
  const jwtPayload = useDecodedJwt();

  // Check current user's role
  console.log('User role:', jwtPayload?.role?.name);
  console.log('User organization:', jwtPayload?.organizationId);

  return (
    <AddPersonnelModal
      open={isOpen}
      onOpenChange={setIsOpen}
      mode="add"
      // Modal automatically adapts based on user's role
    />
  );
}
```

### **Supported Roles:**
- **Organization Admin**: Limited to their organization, cannot see Super Admin role
- **Super Admin**: Full access (role not shown in dropdown for security)
- **Other Roles**: Standard access with appropriate filtering

### Using the API Client:
```jsx
import { api, usePersonnel, useOrganizations } from '@relativity/common-components';

// Using hooks (recommended)
function MyComponent() {
  const { data: personnel, loading, error } = usePersonnel();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return <div>{/* Your component */}</div>;
}

// Using API client directly
async function customApiCall() {
  try {
    const response = await api.get('/custom-endpoint');
    return response.data;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
}
```

## 🔧 Advanced Configuration

### Package.json in Your React Project

Add this to your React project's `package.json` for better dependency management:

```json
{
  "dependencies": {
    "@relativity/common-components": "^1.0.1"
  },
  "resolutions": {
    "react": "^18.3.1",
    "react-dom": "^18.3.1"
  }
}
```

### Automated Update Script

Create a script in your React project to easily update the library:

```bash
# create update-common-components.sh
#!/bin/bash
echo "Updating CommonComponent library..."
cd ../CommonComponent
npm run build
echo "CommonComponent updated! Your React project will use the latest version."
```
