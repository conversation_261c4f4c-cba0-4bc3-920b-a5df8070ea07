import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useEffect, useState } from 'react';
const defaultConfig = {
    defaultTheme: 'system',
    storageKey: 'theme',
    enableSystem: true,
    disableTransitionOnChange: false,
    themes: ['light', 'dark'],
    attribute: 'class',
    value: {},
};
const ThemeProviderContext = createContext(undefined);
export function UniversalThemeProvider({ children, defaultTheme = defaultConfig.defaultTheme, storageKey = defaultConfig.storageKey, enableSystem = defaultConfig.enableSystem, disableTransitionOnChange = defaultConfig.disableTransitionOnChange, themes = defaultConfig.themes, attribute = defaultConfig.attribute, value = defaultConfig.value, }) {
    const [theme, setThemeState] = useState(() => {
        if (typeof window === 'undefined')
            return defaultTheme;
        try {
            const stored = localStorage.getItem(storageKey);
            return stored || defaultTheme;
        }
        catch {
            return defaultTheme;
        }
    });
    const [systemTheme, setSystemTheme] = useState(() => {
        if (typeof window === 'undefined')
            return 'light';
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    });
    const resolvedTheme = theme === 'system' ? systemTheme : theme;
    // Listen for system theme changes
    useEffect(() => {
        if (!enableSystem)
            return;
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const handleChange = (e) => {
            setSystemTheme(e.matches ? 'dark' : 'light');
        };
        mediaQuery.addEventListener('change', handleChange);
        return () => mediaQuery.removeEventListener('change', handleChange);
    }, [enableSystem]);
    // Apply theme to DOM
    useEffect(() => {
        const root = document.documentElement;
        if (disableTransitionOnChange) {
            const css = document.createElement('style');
            css.appendChild(document.createTextNode('*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}'));
            document.head.appendChild(css);
            // Force reflow
            (() => window.getComputedStyle(document.body))();
            // Remove after a frame
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    document.head.removeChild(css);
                });
            });
        }
        if (attribute === 'class') {
            root.classList.remove(...themes);
            root.classList.add(resolvedTheme);
        }
        else {
            root.setAttribute('data-theme', value[resolvedTheme] || resolvedTheme);
        }
    }, [resolvedTheme, attribute, themes, value, disableTransitionOnChange]);
    const setTheme = (newTheme) => {
        try {
            localStorage.setItem(storageKey, newTheme);
        }
        catch {
            // Ignore localStorage errors
        }
        setThemeState(newTheme);
    };
    const contextValue = {
        theme,
        setTheme,
        themes,
        systemTheme,
        resolvedTheme,
    };
    return (_jsx(ThemeProviderContext.Provider, { value: contextValue, children: children }));
}
export const useUniversalTheme = () => {
    const context = useContext(ThemeProviderContext);
    if (context === undefined) {
        throw new Error('useUniversalTheme must be used within a UniversalThemeProvider');
    }
    return context;
};
// Utility functions
export const getSystemTheme = () => {
    if (typeof window === 'undefined')
        return 'light';
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};
export const applyTheme = (theme, config = {}) => {
    const { storageKey = 'theme', attribute = 'class', themes = ['light', 'dark'], value = {}, } = { ...defaultConfig, ...config };
    const resolvedTheme = theme === 'system' ? getSystemTheme() : theme;
    const root = document.documentElement;
    try {
        localStorage.setItem(storageKey, theme);
    }
    catch {
        // Ignore localStorage errors
    }
    if (attribute === 'class') {
        root.classList.remove(...themes);
        root.classList.add(resolvedTheme);
    }
    else {
        root.setAttribute('data-theme', value[resolvedTheme] || resolvedTheme);
    }
};
// Hook for creating theme-aware components
export const useThemeAware = () => {
    const { resolvedTheme, theme, setTheme } = useUniversalTheme();
    const isDark = resolvedTheme === 'dark';
    const isLight = resolvedTheme === 'light';
    const isSystem = theme === 'system';
    const toggleTheme = () => {
        setTheme(resolvedTheme === 'dark' ? 'light' : 'dark');
    };
    const cycleTheme = () => {
        const themes = ['light', 'dark', 'system'];
        const currentIndex = themes.indexOf(theme);
        const nextIndex = (currentIndex + 1) % themes.length;
        setTheme(themes[nextIndex]);
    };
    return {
        theme,
        resolvedTheme,
        setTheme,
        isDark,
        isLight,
        isSystem,
        toggleTheme,
        cycleTheme,
    };
};
export default UniversalThemeProvider;
