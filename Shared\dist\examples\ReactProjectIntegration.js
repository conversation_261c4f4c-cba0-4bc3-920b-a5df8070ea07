import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useEffect, useState } from 'react';
import { SocketProvider, useSocketContext, useSocketLogout } from '../context/SocketContext';
// ===== 1. MAIN APP SETUP (Root Level) =====
/**
 * This is how you set up the socket connection at the root of your React project
 */
export const App = () => {
    // Your authentication state (from Redux, Context, etc.)
    const [isAuthenticated, setIsAuthenticated] = useState(true);
    const [accessToken, setAccessToken] = useState("your-jwt-token");
    // Socket server URL - pass this from your environment or config
    const socketUrl = process.env.REACT_APP_SOCKET_URL || "ws://localhost:8081";
    // Your existing logout function
    const handleAuthLogout = async () => {
        console.log("🔐 Performing authentication logout...");
        // Clear authentication state
        setIsAuthenticated(false);
        setAccessToken("");
        // Clear local storage
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        // Redirect to login or update app state
        window.location.href = '/login';
        // OR if using React Router: navigate('/login');
    };
    return (_jsxs(SocketProvider, { socketUrl: socketUrl, isAuthenticated: isAuthenticated, accessToken: accessToken, onAuthLogout: handleAuthLogout, children: [_jsx(Header, {}), _jsx(MainContent, {}), _jsx(Footer, {})] }));
};
// ===== 2. HEADER COMPONENT (Individual Logout) =====
/**
 * Example of calling logout from individual component
 */
const Header = () => {
    // Get logout function from socket context
    const logout = useSocketLogout();
    const { isConnected } = useSocketContext();
    const handleLogout = async () => {
        try {
            console.log("🚪 Logging out from header...");
            await logout(); // This handles both socket and auth logout
            console.log("✅ Successfully logged out");
        }
        catch (error) {
            console.error("❌ Logout failed:", error);
            // Show error message to user
            alert("Logout failed. Please try again.");
        }
    };
    return (_jsxs("header", { style: { padding: '1rem', borderBottom: '1px solid #ccc' }, children: [_jsx("h1", { children: "My App" }), _jsxs("div", { children: [_jsxs("span", { children: ["Socket: ", isConnected ? '🟢 Connected' : '🔴 Disconnected'] }), _jsx("button", { onClick: handleLogout, style: { marginLeft: '1rem', padding: '0.5rem 1rem' }, children: "Logout" })] })] }));
};
// ===== 3. MAIN CONTENT (Socket Events) =====
/**
 * Example of using socket events in a component
 */
const MainContent = () => {
    const { emit, on, off, isConnected } = useSocketContext();
    const [messages, setMessages] = useState([]);
    useEffect(() => {
        if (isConnected) {
            // Listen for messages
            const handleMessage = (data) => {
                console.log("📨 Message received:", data);
                setMessages(prev => [...prev, data.text]);
            };
            const handleNotification = (data) => {
                console.log("🔔 Notification:", data);
                // Show notification to user
            };
            // Set up event listeners
            on('message', handleMessage);
            on('notification', handleNotification);
            // Cleanup on unmount or disconnect
            return () => {
                off('message', handleMessage);
                off('notification', handleNotification);
            };
        }
    }, [isConnected, on, off]);
    const sendMessage = () => {
        if (isConnected) {
            const message = `Hello from client at ${new Date().toLocaleTimeString()}`;
            emit('sendMessage', { text: message, timestamp: Date.now() });
        }
    };
    return (_jsxs("main", { style: { padding: '2rem' }, children: [_jsx("h2", { children: "Main Content" }), _jsx("div", { style: { marginBottom: '1rem' }, children: _jsx("button", { onClick: sendMessage, disabled: !isConnected, style: { padding: '0.5rem 1rem' }, children: "Send Message" }) }), _jsxs("div", { children: [_jsx("h3", { children: "Messages:" }), _jsx("ul", { children: messages.map((msg, index) => (_jsx("li", { children: msg }, index))) })] })] }));
};
// ===== 4. FOOTER COMPONENT (Another Logout Example) =====
/**
 * Another component that can independently call logout
 */
const Footer = () => {
    const logout = useSocketLogout();
    const { isConnected, connectionError } = useSocketContext();
    const handleQuickLogout = async () => {
        const confirmed = window.confirm("Are you sure you want to logout?");
        if (confirmed) {
            try {
                await logout();
            }
            catch (error) {
                console.error("Quick logout failed:", error);
            }
        }
    };
    return (_jsx("footer", { style: { padding: '1rem', borderTop: '1px solid #ccc', marginTop: '2rem' }, children: _jsxs("div", { style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center' }, children: [_jsxs("div", { children: [_jsxs("p", { children: ["Socket Status: ", isConnected ? 'Connected' : 'Disconnected'] }), connectionError && (_jsxs("p", { style: { color: 'red', fontSize: '0.8rem' }, children: ["Error: ", connectionError] }))] }), _jsx("button", { onClick: handleQuickLogout, style: { padding: '0.5rem 1rem', backgroundColor: '#ff4444', color: 'white' }, children: "Quick Logout" })] }) }));
};
// ===== 5. UTILITY COMPONENT (Custom Hook Usage) =====
/**
 * Example of a utility component that only needs logout functionality
 */
export const LogoutButton = ({ className }) => {
    const logout = useSocketLogout();
    const handleClick = async () => {
        try {
            await logout();
        }
        catch (error) {
            console.error("Logout button failed:", error);
        }
    };
    return (_jsx("button", { onClick: handleClick, className: className, children: "Logout" }));
};
export const AppWithAuthContext = ({ auth }) => {
    const socketUrl = "ws://localhost:8081";
    return (_jsx(SocketProvider, { socketUrl: socketUrl, isAuthenticated: auth.isAuthenticated, accessToken: auth.accessToken || undefined, onAuthLogout: auth.logout, children: _jsx(YourAppComponents, {}) }));
};
// Placeholder component
const YourAppComponents = () => _jsx("div", { children: "Your app components here" });
