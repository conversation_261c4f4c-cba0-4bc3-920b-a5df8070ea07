# UI Common Package

A shared package containing theme and internationalization (i18n) configurations for React applications.

## Features

- **Theme Management**: Light, dark, and system theme support with localStorage persistence
- **Internationalization**: Multi-language support with automatic language detection
- **TypeScript Support**: Full type definitions included
- **React Integration**: Hooks and providers for easy integration

## Installation

```bash
npm install @shared/ui-common
```

## Usage

### Theme Provider

```tsx
import { ThemeProvider, useTheme } from '@shared/ui-common';

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="app-theme">
      <YourApp />
    </ThemeProvider>
  );
}

function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  
  return (
    <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
      Toggle Theme
    </button>
  );
}
```

### I18n Configuration

```tsx
import { initI18n, useI18n } from '@shared/ui-common';

// Initialize with custom resources
initI18n({
  resources: {
    en: {
      translation: {
        welcome: "Welcome",
        // Your custom translations
      }
    },
    es: {
      translation: {
        welcome: "Bienvenido",
        // Your custom translations
      }
    }
  }
});

function MyComponent() {
  const { t, changeLanguage } = useI18n();
  
  return (
    <div>
      <h1>{t('welcome')}</h1>
      <button onClick={() => changeLanguage('es')}>
        Switch to Spanish
      </button>
    </div>
  );
}
```

## Default Translations

The package includes common UI translations for:
- Theme controls (light, dark, system)
- Common actions (save, cancel, edit, delete)
- Navigation elements (home, back, next)
- Status indicators (active, loading, error)
- Form elements (submit, reset, search)

## Supported Languages

- English (en)
- Spanish (es)
- French (fr)

## API Reference

### Theme

#### `ThemeProvider`
- `defaultTheme?: 'light' | 'dark' | 'system'` - Default theme (default: 'system')
- `storageKey?: string` - localStorage key (default: 'ui-theme')

#### `useTheme()`
Returns:
- `theme: 'light' | 'dark' | 'system'` - Current theme
- `setTheme: (theme: Theme) => void` - Function to change theme

### I18n

#### `initI18n(config?: Partial<I18nConfig>)`
Initialize i18n with optional custom configuration.

#### `useI18n()`
Returns:
- `t: (key: string) => string` - Translation function
- `changeLanguage: (lang: string) => void` - Change current language
- `getCurrentLanguage: () => string` - Get current language
- `getAvailableLanguages: () => string[]` - Get available languages

## Configuration

### Custom Theme Storage

```tsx
<ThemeProvider storageKey="my-app-theme">
  <App />
</ThemeProvider>
```

### Custom I18n Resources

```tsx
initI18n({
  fallbackLng: 'en',
  resources: {
    en: { translation: { /* your translations */ } },
    de: { translation: { /* German translations */ } }
  }
});
```