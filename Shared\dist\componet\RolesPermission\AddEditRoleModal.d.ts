import React from "react";
import { Permission } from "../../lib/permissionUtils";
export interface AddEditRoleModalProps {
    open: boolean;
    onClose: () => void;
    role: {
        id?: string;
        name?: string;
        description?: string;
        permissionsList?: Permission[];
    };
    setRole: (role: any) => void;
    isEdit: boolean;
    onSave: () => void;
    availablePermissions: Permission[];
}
declare const AddEditRoleModal: React.FC<AddEditRoleModalProps>;
export default AddEditRoleModal;
