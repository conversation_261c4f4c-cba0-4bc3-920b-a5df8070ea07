import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useAuthWithSocket } from '../hooks/useAuthWithSocket';
import { useSocket } from '../hooks/useSocket';
// Example 1: Using useAuthWithSocket (recommended for most cases)
export const AuthWithSocketExample = () => {
    // Your auth state (from your auth context/hook)
    const isAuthenticated = true; // Replace with actual auth state
    const accessToken = "your-jwt-token"; // Replace with actual token
    const socketUrl = "ws://localhost:8081"; // Pass from parent/config
    // Handle auth logout (your existing logout logic)
    const handleAuthLogout = async () => {
        console.log("Performing auth logout...");
        // Your existing logout logic here:
        // - Clear tokens
        // - Redirect to login
        // - Clear user state
        // etc.
    };
    const socket = useAuthWithSocket({
        socketUrl,
        isAuthenticated,
        accessToken,
        onAuthLogout: handleAuthLogout, // Called when server forces logout
    });
    const handleManualLogout = async () => {
        try {
            await socket.logout(); // This will do socket logout + auth logout
        }
        catch (error) {
            console.error("Logout failed:", error);
        }
    };
    return (_jsxs("div", { children: [_jsx("h2", { children: "Auth with Socket Example" }), _jsxs("p", { children: ["Socket Status: ", socket.isConnected ? 'Connected' : 'Disconnected'] }), socket.connectionError && (_jsxs("p", { style: { color: 'red' }, children: ["Error: ", socket.connectionError] })), _jsx("button", { onClick: handleManualLogout, children: "Logout" })] }));
};
// Example 2: Using useSocket directly (for more control)
export const DirectSocketExample = () => {
    // Your auth state
    const isAuthenticated = true;
    const accessToken = "your-jwt-token";
    const socketUrl = "ws://localhost:8081";
    // Your logout handler
    const handleLogout = async () => {
        console.log("Handling logout from parent...");
        // Your logout logic here
    };
    const socket = useSocket({
        socketUrl,
        isAuthenticated,
        accessToken,
        onLogout: handleLogout, // Called when server forces logout
    });
    const handleManualLogout = async () => {
        try {
            // Only does socket logout - you handle auth logout separately
            await socket.logout();
            // Then handle your auth logout
            await handleLogout();
        }
        catch (error) {
            console.error("Socket logout failed:", error);
            // Still do auth logout even if socket logout fails
            await handleLogout();
        }
    };
    return (_jsxs("div", { children: [_jsx("h2", { children: "Direct Socket Example" }), _jsxs("p", { children: ["Socket Status: ", socket.isConnected ? 'Connected' : 'Disconnected'] }), _jsx("button", { onClick: handleManualLogout, children: "Logout" }), _jsx("button", { onClick: () => socket.emit('customEvent', { data: 'test' }), children: "Send Custom Event" })] }));
};
export const WithAuthContextExample = ({ auth, socketUrl }) => {
    const socket = useAuthWithSocket({
        socketUrl,
        isAuthenticated: auth.isAuthenticated,
        accessToken: auth.accessToken || undefined,
        onAuthLogout: auth.logout,
    });
    return (_jsxs("div", { children: [_jsx("h2", { children: "With Auth Context Example" }), _jsxs("p", { children: ["Auth Status: ", auth.isAuthenticated ? 'Authenticated' : 'Not Authenticated'] }), _jsxs("p", { children: ["Socket Status: ", socket.isConnected ? 'Connected' : 'Disconnected'] }), _jsx("button", { onClick: socket.logout, children: "Logout" })] }));
};
