import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Search, Check } from 'lucide-react';
const SelectionPage = ({ title, subtitle, items, selectedItems, onSelectionChange, multiSelect = true, searchable = true, layout = 'grid', columns = 3, showDescription = true, showPreview = false, onSave, onCancel, saveButtonText = 'Save', cancelButtonText = 'Cancel', emptyMessage = 'No items available', className = '', }) => {
    const [searchTerm, setSearchTerm] = useState('');
    // Filter items based on search term
    const filteredItems = items.filter(item => item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase())));
    const handleItemToggle = (item) => {
        const itemId = item.id || item._id;
        const isSelected = selectedItems.some(selected => (selected.id || selected._id) === itemId);
        let newSelection;
        if (multiSelect) {
            if (isSelected) {
                newSelection = selectedItems.filter(selected => (selected.id || selected._id) !== itemId);
            }
            else {
                newSelection = [...selectedItems, item];
            }
        }
        else {
            newSelection = isSelected ? [] : [item];
        }
        onSelectionChange(newSelection);
    };
    const isItemSelected = (item) => {
        const itemId = item.id || item._id;
        return selectedItems.some(selected => (selected.id || selected._id) === itemId);
    };
    const gridColumns = {
        1: 'grid-cols-1',
        2: 'grid-cols-2',
        3: 'grid-cols-3',
        4: 'grid-cols-4',
        5: 'grid-cols-5',
        6: 'grid-cols-6',
    }[columns] || 'grid-cols-3';
    return (_jsxs("div", { className: `selection-page space-y-6 ${className}`, children: [_jsxs("div", { className: "text-center space-y-2", children: [_jsx("h2", { className: "text-2xl font-bold", children: title }), subtitle && (_jsx("p", { className: "text-gray-600 text-sm", children: subtitle }))] }), searchable && (_jsxs("div", { className: "relative max-w-md mx-auto", children: [_jsx(Search, { className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" }), _jsx(Input, { type: "text", placeholder: "Search items...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value), className: "pl-10" })] })), selectedItems.length > 0 && (_jsxs("div", { className: "text-center", children: [_jsxs(Label, { className: "text-sm font-medium text-gray-600", children: ["Selected (", selectedItems.length, "):"] }), _jsx("div", { className: "flex flex-wrap justify-center gap-2 mt-2", children: selectedItems.map((item) => (_jsx(Badge, { variant: "secondary", className: "text-xs", children: item.name }, item.id || item._id))) })] })), filteredItems.length === 0 ? (_jsx("div", { className: "text-center py-12 text-gray-500", children: emptyMessage })) : (_jsx("div", { className: layout === 'grid'
                    ? `grid ${gridColumns} gap-4`
                    : 'space-y-3', children: filteredItems.map((item) => {
                    const isSelected = isItemSelected(item);
                    return (_jsx(Card, { className: `cursor-pointer transition-all duration-200 hover:shadow-md ${isSelected
                            ? 'ring-2 ring-blue-500 border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'}`, onClick: () => handleItemToggle(item), children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "flex items-start justify-between mb-3", children: [_jsxs("div", { className: "flex-1", children: [item.icon && (_jsx("div", { className: "mb-2 text-gray-600", children: item.icon })), _jsx("h3", { className: "font-medium text-sm", children: item.name }), showDescription && item.description && (_jsx("p", { className: "text-xs text-gray-500 mt-1", children: item.description }))] }), isSelected && (_jsx("div", { className: "flex-shrink-0 ml-2", children: _jsx("div", { className: "w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center", children: _jsx(Check, { className: "w-3 h-3 text-white" }) }) }))] }), showPreview && item.preview && (_jsx("div", { className: "mt-3 p-2 bg-gray-50 rounded border", children: item.preview }))] }) }, item.id || item._id));
                }) })), (onSave || onCancel) && (_jsxs("div", { className: "flex justify-center space-x-3 pt-4", children: [onCancel && (_jsx(Button, { variant: "outline", onClick: onCancel, children: cancelButtonText })), onSave && (_jsx(Button, { onClick: onSave, children: saveButtonText }))] }))] }));
};
export default SelectionPage;
