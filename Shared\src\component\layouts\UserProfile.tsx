import { LogOut, User } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
} from "../ui/select";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { useTranslation } from "react-i18next";
import { useDecodedJwt } from "../../hooks/useDecodedJwt";

interface UserProfileProps {
    userImage?: string;
    userName?: string;
    userInitials?: string;
    onLogout?: () => void;
}

export function UserProfile({
    onLogout,
}: UserProfileProps) {
    const { t } = useTranslation();

    const jwtPayload = useDecodedJwt();
    const firstName = jwtPayload?.firstName || "";
    const lastName = jwtPayload?.lastName || "";
    const userName = `${firstName} ${lastName}`.trim() || "";
    const userInitials = `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase() || "U";


    const handleLogout = () => {
        if (onLogout) onLogout();
        // else logout(); // Uncomment if using useAuth
    };

    const handleAction = (action: string) => {
        switch (action) {
            case "profile":
                console.log("Profile clicked");
                break;
            case "logout":
                handleLogout();
                break;
            default:
                break;
        }
    };

    return (
        <div className="flex items-center gap-3">
            {/* Avatar inside Select Trigger */}
            {/* User Name for medium screens and above */}
            <div className="hidden md:block text-sm">
                <div className="font-medium">{userName}</div>
            </div>
            <Select onValueChange={handleAction}>
                <SelectTrigger style={{ border: "none", boxShadow: "none", outline: "none", width: '20px', marginRight: "5px" }} className="focus:ring-0 focus:outline-none ring-0 outline-none">
                    <Avatar style={{ border: "none" }}>
                        <AvatarFallback>{userInitials}</AvatarFallback>
                    </Avatar>
                </SelectTrigger>
                <SelectContent className="border-0 shadow-none">
                    <SelectItem value="profile">
                        <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span>{t("Profile")}</span>
                        </div>
                    </SelectItem>
                    <SelectItem value="logout">
                        <div className="flex items-center gap-2">
                            <LogOut className="h-4 w-4" />
                            <span>{t("Logout")}</span>
                        </div>
                    </SelectItem>
                </SelectContent>
            </Select>


        </div>
    );
}

export default UserProfile;
