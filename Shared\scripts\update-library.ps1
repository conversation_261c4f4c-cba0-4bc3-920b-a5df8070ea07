# PowerShell script to update and distribute the library

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("patch", "minor", "major")]
    [string]$VersionType,
    
    [string]$Message = "Library update"
)

Write-Host "🔧 Building library..." -ForegroundColor Blue
npm run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "📦 Updating version ($VersionType)..." -ForegroundColor Blue
npm version $VersionType

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Version update failed!" -ForegroundColor Red
    exit 1
}

$newVersion = (Get-Content package.json | ConvertFrom-Json).version
Write-Host "✅ Updated to version $newVersion" -ForegroundColor Green

Write-Host "📝 Committing changes..." -ForegroundColor Blue
git add .
git commit -m "$Message - v$newVersion"
git tag "v$newVersion"

Write-Host "🚀 Pushing to repository..." -ForegroundColor Blue
git push origin main --tags

Write-Host "✅ Library updated and published!" -ForegroundColor Green
Write-Host "📋 To update consuming projects:" -ForegroundColor Yellow
Write-Host "   npm update @common/cloud_rps" -ForegroundColor Gray
Write-Host "   or" -ForegroundColor Gray
Write-Host "   npm install @common/cloud_rps@$newVersion" -ForegroundColor Gray
