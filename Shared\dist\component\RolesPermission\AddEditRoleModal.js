import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, Di<PERSON>Title, DialogFooter, } from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Save, X } from "lucide-react";
import { useLanguage } from "../../i18n/LanguageProvider";
import PermissionSelector from "../PermissionSelector";
const AddEditRoleModal = ({ open, onClose, role, setRole, isEdit, onSave, availablePermissions, }) => {
    const { t } = useLanguage();
    const handlePermissionToggle = (permissionId) => {
        const currentPermissions = role.permissionsList || [];
        const exists = currentPermissions.some((p) => p._id === permissionId);
        let newPermissionsList;
        if (exists) {
            // Remove the permission object
            newPermissionsList = currentPermissions.filter((p) => p._id !== permissionId);
        }
        else {
            // Add the permission object
            const permissionToAdd = availablePermissions.find((p) => p._id === permissionId);
            if (permissionToAdd) {
                newPermissionsList = [...currentPermissions, permissionToAdd];
            }
            else {
                newPermissionsList = currentPermissions;
            }
        }
        setRole({ ...role, permissionsList: newPermissionsList });
    };
    const handleSave = () => {
        onSave();
    };
    return (_jsx(Dialog, { open: open, onOpenChange: onClose, children: _jsx(DialogContent, { className: "p-0 gap-0 overflow-hidden", style: {
                maxWidth: "90vw",
                width: "70%",
                maxHeight: "90vh",
                height: "100%",
                minHeight: "600px",
            }, children: _jsxs("div", { style: { display: "flex", flexDirection: "column", height: "100%" }, children: [_jsx(DialogHeader, { className: "px-6 py-4 border-b", style: { flexShrink: 0 }, children: _jsx(DialogTitle, { className: "text-lg font-semibold", children: isEdit ? t("roles.edit") : t("roles.create") }) }), _jsx("div", { className: "px-6 py-4", style: {
                            flex: 1,
                            overflowY: "auto",
                            //minHeight: 0,
                            //maxHeight: 'calc(90vh - 140px)'
                        }, children: _jsxs("div", { style: { display: "flex", flexDirection: "column", gap: "24px" }, children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "roleName", className: "text-sm font-medium", children: t("roles.roleName") }), _jsx(Input, { id: "roleName", value: role.name || "", onChange: (e) => setRole({ ...role, name: e.target.value }), placeholder: t("roles.roleNamePlaceholder"), className: "mt-1" })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "roleDescription", className: "text-sm font-medium", children: t("roles.description") }), _jsx(Textarea, { id: "roleDescription", value: role.description || "", onChange: (e) => setRole({ ...role, description: e.target.value }), placeholder: t("roles.descriptionPlaceholder"), rows: 2, className: "mt-1" })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-lg font-semibold mb-4", children: t("roles.permissions") }), _jsx(PermissionSelector, { availablePermissions: availablePermissions, selectedPermissions: role.permissionsList || [], onTogglePermission: handlePermissionToggle })] })] }) }), _jsx(DialogFooter, { className: "px-6 py-4 border-t bg-background-50", style: {
                            flexShrink: 0,
                            position: "sticky",
                            bottom: 0,
                            zIndex: 10,
                        }, children: _jsxs("div", { className: "flex gap-3 w-full justify-end", children: [_jsxs(Button, { variant: "outline", onClick: onClose, className: "min-w-[100px]", children: [_jsx(X, { className: "w-4 h-4 mr-2" }), t("common.cancel")] }), _jsxs(Button, { onClick: handleSave, className: "min-w-[120px]", children: [_jsx(Save, { className: "w-4 h-4 mr-2" }), isEdit ? t("roles.update") : t("roles.create")] })] }) })] }) }) }));
};
export default AddEditRoleModal;
