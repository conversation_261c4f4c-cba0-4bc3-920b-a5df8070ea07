import i18n from 'i18next';
import { I18nConfig, LanguageResources } from './types';
export declare const defaultResources: LanguageResources;
export declare const initI18n: (customConfig?: Partial<I18nConfig>) => Promise<import("i18next").TFunction<"translation", undefined>>;
export declare const mergeResources: (defaultRes: LanguageResources, customRes: LanguageResources) => LanguageResources;
export declare const useI18n: () => {
    t: any;
    changeLanguage: (language: string) => void;
    getCurrentLanguage: () => any;
    getAvailableLanguages: () => string[];
    i18n: any;
};
export declare const addResources: (language: string, namespace: string, resources: Record<string, string>) => void;
export declare const getCurrentLanguage: () => string;
export declare const changeLanguage: (language: string) => Promise<import("i18next").TFunction<"translation", undefined>>;
export default i18n;
