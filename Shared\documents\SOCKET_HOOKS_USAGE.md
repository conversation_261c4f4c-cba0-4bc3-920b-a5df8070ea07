# Socket Hooks Usage Guide

This document provides comprehensive guidance on how to use the socket-related hooks in your React application.

## Overview

We provide two main hooks for socket management:

1. **`useSocket`** - Core socket functionality with authentication
2. **`useAuthWithSocket`** - Integrated socket + authentication management (recommended)

## Quick Start

### 1. Basic Usage with `useAuthWithSocket` (Recommended)

```typescript
import React from 'react';
import { useAuthWithSocket } from './hooks/useAuthWithSocket';

const MyComponent = () => {
  // Your authentication state (from context, Redux, etc.)
  const isAuthenticated = true;
  const accessToken = "your-jwt-token";
  const socketUrl = "ws://localhost:8081";

  // Your existing logout function
  const handleAuthLogout = async () => {
    // Clear tokens, redirect to login, etc.
    localStorage.removeItem('token');
    window.location.href = '/login';
  };

  const socket = useAuthWithSocket({
    socketUrl,
    isAuthenticated,
    accessToken,
    onAuthLogout: handleAuthLogout,
  });

  return (
    <div>
      <p>Socket Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
      {socket.connectionError && (
        <p style={{ color: 'red' }}>Error: {socket.connectionError}</p>
      )}
      <button onClick={socket.logout}>Logout</button>
    </div>
  );
};
```

### 2. Integration with Auth Context

```typescript
import React, { useContext } from 'react';
import { AuthContext } from './AuthContext';
import { useAuthWithSocket } from './hooks/useAuthWithSocket';

const MyApp = () => {
  const { isAuthenticated, accessToken, logout } = useContext(AuthContext);
  
  const socket = useAuthWithSocket({
    socketUrl: process.env.REACT_APP_SOCKET_URL || "ws://localhost:8081",
    isAuthenticated,
    accessToken,
    onAuthLogout: logout, // Your existing logout function
  });

  const handleManualLogout = async () => {
    try {
      await socket.logout(); // Handles both socket and auth logout
      console.log('Successfully logged out');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div>
      <h1>My App</h1>
      <p>Auth Status: {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>
      <p>Socket Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
      <button onClick={handleManualLogout}>Logout</button>
    </div>
  );
};
```

## Advanced Usage

### 3. Using `useSocket` Directly (For More Control)

```typescript
import React, { useEffect } from 'react';
import { useSocket } from './hooks/useSocket';

const AdvancedComponent = () => {
  const isAuthenticated = true;
  const accessToken = "your-jwt-token";
  
  const socket = useSocket({
    socketUrl: "ws://localhost:8081",
    isAuthenticated,
    accessToken,
    onLogout: async () => {
      console.log("Server forced logout");
      // Handle server-forced logout
    },
  });

  useEffect(() => {
    if (socket.isConnected) {
      // Listen for custom events
      socket.on('notification', (data) => {
        console.log('Notification received:', data);
      });

      socket.on('userMessage', (message) => {
        console.log('Message:', message);
      });
    }

    return () => {
      // Cleanup event listeners
      socket.off('notification');
      socket.off('userMessage');
    };
  }, [socket.isConnected]);

  const sendMessage = () => {
    socket.emit('sendMessage', {
      text: 'Hello from client',
      timestamp: Date.now()
    });
  };

  const handleLogout = async () => {
    try {
      // Only handles socket logout
      await socket.logout();
      // You need to handle auth logout separately
      // await yourAuthLogout();
    } catch (error) {
      console.error('Socket logout failed:', error);
    }
  };

  return (
    <div>
      <p>Socket Status: {socket.isConnected ? 'Connected' : 'Disconnected'}</p>
      <button onClick={sendMessage} disabled={!socket.isConnected}>
        Send Message
      </button>
      <button onClick={handleLogout}>Logout</button>
    </div>
  );
};
```

## API Reference

### `useAuthWithSocket(options)`

**Parameters:**
- `options.socketUrl?: string` - WebSocket server URL (default: "http://localhost:8081")
- `options.isAuthenticated?: boolean` - Authentication status (default: false)
- `options.accessToken?: string` - JWT access token for authentication
- `options.onAuthLogout?: () => void | Promise<void>` - Callback for authentication logout

**Returns:**
- All properties from `useSocket` plus:
- `logout(): Promise<void>` - Integrated logout (socket + auth)

### `useSocket(options)`

**Parameters:**
- `options.socketUrl?: string` - WebSocket server URL
- `options.isAuthenticated?: boolean` - Authentication status
- `options.accessToken?: string` - JWT access token
- `options.onLogout?: () => void | Promise<void>` - Callback for server-forced logout

**Returns:**
- `socket` - Socket.IO instance
- `isConnected: boolean` - Connection status
- `connectionError: string | null` - Connection error message
- `emit(event, data)` - Send event to server
- `on(event, callback)` - Listen for events
- `off(event, callback?)` - Remove event listeners
- `disconnect()` - Disconnect socket
- `reconnect()` - Reconnect socket
- `logout(): Promise<void>` - Socket logout only

## Event Handling

### Server Events You Can Listen For:

```typescript
// Connection events (handled automatically)
socket.on('connect', () => console.log('Connected'));
socket.on('disconnect', (reason) => console.log('Disconnected:', reason));

// Custom events (you handle these)
socket.on('notification', (data) => {
  console.log('Notification:', data);
});

socket.on('userMessage', (message) => {
  console.log('Message:', message);
});

// Server-forced logout (handled automatically via onLogout callback)
socket.on('userLogout', (data) => {
  // This is handled internally, but you can still listen if needed
  console.log('Forced logout:', data);
});
```

### Client Events You Can Emit:

```typescript
// Send custom events
socket.emit('sendMessage', { text: 'Hello', userId: 123 });
socket.emit('joinRoom', { roomId: 'room-123' });
socket.emit('updateStatus', { status: 'online' });

// Logout (handled by logout() method)
socket.emit('logout'); // Don't call directly, use socket.logout()
```

## Error Handling

```typescript
const socket = useAuthWithSocket({
  socketUrl: "ws://localhost:8081",
  isAuthenticated: true,
  accessToken: "your-token",
  onAuthLogout: async () => {
    try {
      // Your logout logic
      await performLogout();
    } catch (error) {
      console.error('Logout failed:', error);
      // Handle logout failure
    }
  }
});

// Check for connection errors
if (socket.connectionError) {
  console.error('Socket connection failed:', socket.connectionError);
  // Show error message to user
}

// Handle logout errors
const handleLogout = async () => {
  try {
    await socket.logout();
  } catch (error) {
    console.error('Logout failed:', error);
    // Show error message to user
  }
};
```

## Best Practices

1. **Use `useAuthWithSocket` for most cases** - It provides integrated logout handling
2. **Pass socket URL from parent/config** - Don't hardcode URLs
3. **Handle connection errors gracefully** - Show appropriate UI feedback
4. **Clean up event listeners** - Use useEffect cleanup functions
5. **Handle server-forced logout** - Provide onLogout callback for security
6. **Don't emit logout directly** - Use the provided logout() method

## Troubleshooting

### Common Issues:

1. **Socket not connecting:**
   - Check if `isAuthenticated` is true
   - Verify `accessToken` is provided
   - Check socket URL is correct

2. **Events not received:**
   - Ensure socket is connected (`socket.isConnected`)
   - Check event listener is set up correctly
   - Verify server is emitting the event

3. **Logout not working:**
   - Check if `onAuthLogout` callback is provided
   - Verify server responds to logout event
   - Check for network connectivity

### Debug Logging:

The hooks include comprehensive console logging. Check browser console for:
- Connection status messages
- Event emissions and receptions
- Error messages
- Logout flow progress
