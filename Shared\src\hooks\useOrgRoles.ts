import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "../lib/api";
import { useToast } from "../component/ui/use-toast";
import { Permission } from "../lib/permissionUtils";

// Define Role types (same as useRoles)
export interface OrgRole {
  id: string;
  _id?: string;
  name: string;
  description: string;
  permissionsList: Permission[];
}

export interface CreateOrgRoleData {
  name: string;
  description: string;
  permissionsList: Permission[];
}

export interface UpdateOrgRoleData extends CreateOrgRoleData {
  id: string;
}

// Organization Roles API functions
export const orgRolesApi = {
  // Get organization roles
  getOrgRoles: async (organizationId?: string): Promise<any> => {
    try {
      const endpoint = "/api/roles";

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (organizationId) {
        queryParams.append("organizationId", organizationId);
      }

      const queryString = queryParams.toString();
      const fullEndpoint = `${endpoint}${queryString ? `?${queryString}` : ""}`;

      const response = await api.get(fullEndpoint);
      const json = response.data;

      if (!json.success || !Array.isArray(json.data)) {
        throw new Error("Invalid response format");
      }

      // Normalize the roles data
      const normalizedData = json.data.map((role: any) => ({
        ...role,
        id: role.id ?? role._id, // Normalize id
      }));

      return {
        ...json,
        data: normalizedData,
      };
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to access this resource");
      }
      throw new Error("Failed to fetch organization roles");
    }
  },

  // Get organization roles simple
  getOrgRolesSimple: async (organizationId?: string): Promise<any> => {
    const response = await orgRolesApi.getOrgRoles(organizationId);
    if (!response || !Array.isArray(response.data)) {
      throw new Error("You do not have permission to access this resource");
    }

    return { data: response.data };
  },

  // Get single organization role
  getOrgRole: async (
    id: string,
    organizationId?: string
  ): Promise<OrgRole | null> => {
    try {
      const endpoint = `/api/roles/${id}`;

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (organizationId) {
        queryParams.append("organizationId", organizationId);
      }

      const queryString = queryParams.toString();
      const fullEndpoint = `${endpoint}${queryString ? `?${queryString}` : ""}`;

      const response = await api.get(fullEndpoint);
      const result = response.data.data || response.data;

      return result;
    } catch (error: any) {
      if (error.response?.status === 404) return null;
      throw new Error("Failed to fetch organization role");
    }
  },

  // Create organization role
  createOrgRole: async (
    data: CreateOrgRoleData,
    organizationId?: string
  ): Promise<OrgRole> => {
    try {
      const endpoint = "/api/roles";

      // Include organizationId in the request body if provided
      const requestData = organizationId ? { ...data, organizationId } : data;

      const response = await api.post(endpoint, requestData);
      const result = response.data.data || response.data;

      return result;
    } catch (error: any) {
      throw new Error("Failed to create organization role");
    }
  },

  // Update organization role
  updateOrgRole: async (
    data: UpdateOrgRoleData,
    organizationId?: string
  ): Promise<OrgRole> => {
    try {
      const { id, ...updateData } = data;
      const endpoint = `/api/roles/${id}`;

      // Include organizationId in the request body if provided
      const requestData = organizationId
        ? { ...updateData, organizationId }
        : updateData;

      const response = await api.put(endpoint, requestData);
      const result = response.data.data || response.data;

      return result;
    } catch (error: any) {
      throw new Error("Failed to update organization role");
    }
  },

  // Delete organization role
  deleteOrgRole: async (id: string, organizationId?: string): Promise<void> => {
    try {
      const endpoint = `/api/roles/${id}`;

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (organizationId) {
        queryParams.append("organizationId", organizationId);
      }

      const queryString = queryParams.toString();
      const fullEndpoint = `${endpoint}${queryString ? `?${queryString}` : ""}`;

      await api.delete(fullEndpoint);
    } catch (error: any) {
      throw new Error("Failed to delete organization role");
    }
  },
};

// Query keys for organization roles
export const orgRoleKeys = {
  all: ["orgRoles"] as const,
  lists: () => [...orgRoleKeys.all, "list"] as const,
  list: () => [...orgRoleKeys.lists()] as const,
  details: () => [...orgRoleKeys.all, "detail"] as const,
  detail: (id: string) => [...orgRoleKeys.details(), id] as const,
};

// Organization Roles Hooks
export const useOrgRolesWithPagination = (organizationId?: string) => {
  return useQuery({
    queryKey: [...orgRoleKeys.list(), organizationId || "all"],
    queryFn: () => orgRolesApi.getOrgRoles(organizationId),
    staleTime: 5 * 60 * 1000,
  });
};

export const useOrgRoles = (organizationId?: string) => {
  return useQuery({
    queryKey: [...orgRoleKeys.lists(), organizationId || "all"],
    queryFn: () => orgRolesApi.getOrgRolesSimple(organizationId),
    staleTime: 5 * 60 * 1000,
  });
};

export const useOrgRole = (id: string, organizationId?: string) => {
  return useQuery<OrgRole | null>({
    queryKey: [...orgRoleKeys.detail(id), organizationId || "all"],
    queryFn: () => orgRolesApi.getOrgRole(id, organizationId),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Organization Role Mutations
export const useCreateOrgRole = (organizationId?: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (role: CreateOrgRoleData) =>
      orgRolesApi.createOrgRole(role, organizationId),
    onSuccess: (newRole) => {
      queryClient.setQueryData(
        [...orgRoleKeys.lists(), organizationId || "all"],
        (old: any) => {
          if (!old || !Array.isArray(old.data)) {
            return {
              data: [newRole],
            };
          }

          return {
            ...old,
            data: [...old.data, newRole],
          };
        }
      );

      queryClient.invalidateQueries({
        queryKey: [...orgRoleKeys.lists(), organizationId || "all"],
      });

      toast.toast({
        title: "Success",
        description: `Organization role "${newRole.name}" created successfully!`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to create organization role:", error);

      toast.toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create organization role. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateOrgRole = (organizationId?: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (role: UpdateOrgRoleData) =>
      orgRolesApi.updateOrgRole(role, organizationId),
    onSuccess: (updatedRole) => {
      queryClient.setQueryData(
        [...orgRoleKeys.lists(), organizationId || "all"],
        (old: any) => {
          if (!old || !Array.isArray(old.data)) return old;

          return {
            ...old,
            data: old.data.map((role: any) =>
              role.id === updatedRole.id ? updatedRole : role
            ),
          };
        }
      );

      queryClient.invalidateQueries({
        queryKey: [...orgRoleKeys.lists(), organizationId || "all"],
      });

      toast.toast({
        title: "Success",
        description: `Organization role "${updatedRole.name}" updated successfully!`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to update organization role:", error);

      toast.toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to update organization role. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteOrgRole = (organizationId?: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (roleId: string) =>
      orgRolesApi.deleteOrgRole(roleId, organizationId),
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData(
        [...orgRoleKeys.lists(), organizationId || "all"],
        (old: any) => {
          if (!old || !Array.isArray(old.data)) return old;

          return {
            ...old,
            data: old.data.filter((role: any) => role.id !== deletedId),
          };
        }
      );

      queryClient.invalidateQueries({
        queryKey: [...orgRoleKeys.lists(), organizationId || "all"],
      });

      toast.toast({
        title: "Success",
        description: "Organization role deleted successfully!",
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to delete organization role:", error);

      toast.toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to delete organization role. Please try again.",
        variant: "destructive",
      });
    },
  });
};
