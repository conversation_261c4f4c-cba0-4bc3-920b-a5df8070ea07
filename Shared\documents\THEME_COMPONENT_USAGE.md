# Theme Component Usage Guide

This guide shows how to use the CommonComponent theme components in other React projects.

## Installation

```bash
npm install next-themes @relativity/common-components
```

## Setup

### 1. Install and Setup next-themes

First, wrap your app with the ThemeProvider from next-themes:

```tsx
// App.tsx or _app.tsx
import { ThemeProvider } from 'next-themes'

function MyApp({ Component, pageProps }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <Component {...pageProps} />
    </ThemeProvider>
  )
}

export default MyApp
```

### 2. Add CSS Variables

Add these CSS variables to your global CSS file:

```css
/* globals.css */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
```

## Usage

### 1. Basic Theme Toggle (Select Dropdown)

```tsx
import { ThemeToggle } from '@relativity/common-components';

function Header() {
  return (
    <header className="flex justify-between items-center p-4">
      <h1>My App</h1>
      <ThemeToggle />
    </header>
  );
}
```

### 2. Icon-Only Theme Toggle Button

```tsx
import { ThemeToggleButton } from '@relativity/common-components';

function Navbar() {
  return (
    <nav className="flex items-center gap-4">
      <span>My App</span>
      <ThemeToggleButton />
    </nav>
  );
}
```

### 3. Custom Implementation

```tsx
import { useTheme } from 'next-themes';
import { Button } from '@relativity/common-components';

function CustomThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <div className="flex gap-2">
      <Button 
        variant={theme === 'light' ? 'default' : 'outline'}
        onClick={() => setTheme('light')}
      >
        Light
      </Button>
      <Button 
        variant={theme === 'dark' ? 'default' : 'outline'}
        onClick={() => setTheme('dark')}
      >
        Dark
      </Button>
      <Button 
        variant={theme === 'system' ? 'default' : 'outline'}
        onClick={() => setTheme('system')}
      >
        System
      </Button>
    </div>
  );
}
```

## Component Variants

### ThemeToggle (Select Dropdown)
- Shows current theme with icon
- Dropdown with all theme options
- Icons for each theme option

```tsx
<ThemeToggle />
```

### ThemeToggleButton (Icon Only)
- Cycles through themes on click
- Shows current theme icon
- Compact for toolbars/navbars

```tsx
<ThemeToggleButton />
```

## Complete Example

```tsx
// App.tsx
import React from 'react';
import { ThemeProvider } from 'next-themes';
import { ThemeToggle, ThemeToggleButton } from '@relativity/common-components';

function App() {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <div className="min-h-screen bg-background text-foreground">
        {/* Header with theme toggle */}
        <header className="border-b p-4">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-bold">My App</h1>
            <div className="flex items-center gap-3">
              {/* Icon button for mobile */}
              <div className="md:hidden">
                <ThemeToggleButton />
              </div>
              {/* Dropdown for desktop */}
              <div className="hidden md:block">
                <ThemeToggle />
              </div>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="p-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">Welcome to My App</h2>
            <p className="text-muted-foreground">
              This app supports light, dark, and system themes.
            </p>
          </div>
        </main>
      </div>
    </ThemeProvider>
  );
}

export default App;
```

## TypeScript Support

The components are fully typed and work seamlessly with TypeScript:

```tsx
import type { ComponentProps } from 'react';
import { ThemeToggle } from '@relativity/common-components';

// Extract component props type
type ThemeToggleProps = ComponentProps<typeof ThemeToggle>;

// Use in your own components
function MyThemeToggle(props: ThemeToggleProps) {
  return <ThemeToggle {...props} />;
}
```

## Styling

The components use CSS variables and work with any CSS framework:

```css
/* Custom styling */
.theme-toggle {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

.theme-toggle:hover {
  background-color: hsl(var(--accent));
}
```

## Framework Compatibility

- ✅ **Next.js** - Full SSR support
- ✅ **Create React App** - Works out of the box
- ✅ **Vite** - Fast development
- ✅ **Remix** - Server-side rendering
- ✅ **Gatsby** - Static site generation

## Key Features

- 🎨 **Beautiful UI** - Polished design with icons
- 🌓 **Theme Support** - Light, dark, and system themes
- 📱 **Responsive** - Works on all screen sizes
- ♿ **Accessible** - Keyboard navigation and screen readers
- 🔧 **Customizable** - Easy to style and extend
- 📦 **Lightweight** - Minimal bundle size
- 🚀 **Fast** - Optimized performance

## Troubleshooting

### Hydration Mismatch
If you see hydration errors, make sure to set `suppressHydrationWarning` on your html element:

```tsx
<html suppressHydrationWarning>
```

### Theme Not Persisting
Ensure localStorage is available and the ThemeProvider is properly configured with a `storageKey`.

### Icons Not Showing
Make sure you have `lucide-react` installed:

```bash
npm install lucide-react
```
