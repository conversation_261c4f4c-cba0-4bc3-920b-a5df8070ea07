import React from 'react';
export interface SelectionItem {
    id: string;
    _id?: string;
    name: string;
    description?: string;
    icon?: React.ReactNode;
    preview?: React.ReactNode;
    category?: string;
    [key: string]: any;
}
export interface SelectionPageProps {
    title: string;
    subtitle?: string;
    items: SelectionItem[];
    selectedItems: SelectionItem[];
    onSelectionChange: (selectedItems: SelectionItem[]) => void;
    multiSelect?: boolean;
    searchable?: boolean;
    layout?: 'grid' | 'list';
    columns?: number;
    showDescription?: boolean;
    showPreview?: boolean;
    onSave?: () => void;
    onCancel?: () => void;
    saveButtonText?: string;
    cancelButtonText?: string;
    emptyMessage?: string;
    className?: string;
}
declare const SelectionPage: React.FC<SelectionPageProps>;
export default SelectionPage;
