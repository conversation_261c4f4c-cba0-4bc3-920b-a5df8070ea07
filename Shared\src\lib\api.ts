// Shared API configuration using axios
import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig, AxiosRequestConfig } from 'axios';

// Default API URL (fallback)
const DEFAULT_API_URL = "https://cloud-api-admin-dev.rpsmobile.com";

// Global variable to store API URL passed from parent app
let externalApiUrl: string | null = null;

// Function to set API URL from parent app
export const setApiUrl = (apiUrl: string): void => {
  externalApiUrl = apiUrl;
  // Update the axios instance with new base URL
  apiClient.defaults.baseURL = apiUrl;
};

// Get the API base URL
const getApiBaseUrl = (): string => {
  // Use external API URL if provided, otherwise use default
  return externalApiUrl || DEFAULT_API_URL;
};

const API_BASE_URL = getApiBaseUrl();

// Export configuration for debugging/testing
export const apiConfig = {
  currentUrl: API_BASE_URL,
  apiUrl: externalApiUrl || DEFAULT_API_URL,
  environment: typeof window !== 'undefined' ? window.location.hostname : 'server'
};

// Create main axios instance
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token from localStorage
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('access_token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('access_token');
      console.error('Authentication failed:', error.response.data?.message);
    } else if (error.response?.status === 403) {
      // Forbidden - user doesn't have permission
      console.error('Access forbidden:', error.response.data?.message);
    } else if (error.response?.status >= 500) {
      // Server error
      console.error('Server error:', error.response.data?.message);
    }

    return Promise.reject(error);
  }
);

// Helper functions for common API operations
export const api = {
  // GET request
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.get<T>(url, config);
  },

  // POST request
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.post<T>(url, data, config);
  },

  // PUT request
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.put<T>(url, data, config);
  },

  // PATCH request
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.patch<T>(url, data, config);
  },

  // DELETE request
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.delete<T>(url, config);
  },
};

// Export the axios instance for direct use if needed
export default apiClient;
