import { useSocket } from "./useSocket";

interface UseAuthWithSocketOptions {
  socketUrl?: string;
  isAuthenticated?: boolean;
  accessToken?: string;
  onAuthLogout?: () => void | Promise<void>;
}

/**
 * Custom hook that combines authentication and socket functionality
 * Provides an integrated logout that handles both socket and auth logout
 */
export const useAuthWithSocket = (options: UseAuthWithSocketOptions = {}) => {
  const { socketUrl, isAuthenticated, accessToken, onAuthLogout } = options;
  
  const socket = useSocket({
    socketUrl,
    isAuthenticated,
    accessToken,
    onLogout: onAuthLogout, // This will be called when server forces logout
  });

  // Integrated logout function that uses socket logout first, then auth logout
  const logout = async () => {
    try {
      // First, perform socket logout if connected
      if (socket.isConnected && socket.logout) {
        console.log("🔌 Performing socket logout...");
        await socket.logout();
      }
    } catch (error) {
      console.error("❌ Socket logout failed:", error);
      // Continue with regular logout even if socket logout fails
    }

    // Then perform regular auth logout
    try {
      if (onAuthLogout) {
        await onAuthLogout();
      }
    } catch (error) {
      console.error("❌ Auth logout failed:", error);
      throw error;
    }
  };

  return {
    ...socket,
    logout, // Override the socket logout with integrated logout
  };
};

export default useAuthWithSocket;
