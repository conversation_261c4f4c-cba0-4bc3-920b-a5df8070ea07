import { type LanguageConfig, type Language } from './LanguageProvider';
export declare const languages: Language[];
export declare const reactLanguageConfig: LanguageConfig;
export declare function createLanguageConfig(overrides?: Partial<LanguageConfig>): LanguageConfig;
export declare const commonTranslations: {
    en: {
        'header.title': string;
        'language.select': string;
        'language.current': string;
        'theme.select': string;
        'theme.light': string;
        'theme.dark': string;
        'theme.system': string;
        'common.save': string;
        'common.cancel': string;
        'common.apply': string;
        'common.close': string;
        'common.settings': string;
        'common.preferences': string;
        'common.loading': string;
        'common.retry': string;
        'common.error': string;
        'nav.home': string;
        'nav.about': string;
        'nav.contact': string;
        'nav.help': string;
        'nav.dashboard': string;
        'nav.personnel': string;
        'nav.organizations': string;
        'nav.roles': string;
        'dashboard.title': string;
        'dashboard.subtitle': string;
        'dashboard.totalOrgs': string;
        'dashboard.activeUsers': string;
        'dashboard.licenseRevenue': string;
        'dashboard.growthRate': string;
        'dashboard.recentOrgs': string;
        'dashboard.licenseUsage': string;
        'dashboard.licensesSold': string;
        'dashboard.utilizationRate': string;
        'dashboard.noRecentOrgs': string;
        'dashboard.loading': string;
        'dashboard.noData': string;
        'dashboard.error': string;
        'organizations.title': string;
        'organizations.subtitle': string;
        'organizations.addOrganization': string;
        'organizations.addOrganizations': string;
        'organizations.search': string;
        'organizations.users': string;
        'organizations.month': string;
        'organizations.viewDetails': string;
        'organizations.manage': string;
        'organizations.details': string;
        'organizations.orgId': string;
        'organizations.domain': string;
        'organizations.domainPlaceholder': string;
        'organizations.createdDate': string;
        'organizations.contactEmail': string;
        'organizations.lastLogin': string;
        'organizations.usageStats': string;
        'organizations.monthlyRevenue': string;
        'organizations.annualRevenue': string;
        'organizations.licenseUtilization': string;
        'organizations.license': string;
        'organizations.licenses': string;
        'organizations.current': string;
        'organizations.currentBillingPeriod': string;
        'organizations.currentUsage': string;
        'organizations.billingInformation': string;
        'organizations.billingCycle': string;
        'organizations.nextBillingDate': string;
        'organizations.paymentMethod': string;
        'organizations.totalLicenses': string;
        'organizations.status': string;
        'organizations.status.active': string;
        'organizations.status.pending': string;
        'organizations.status.suspended': string;
        'organizations.organizationName': string;
        'organizations.organizationNamePlaceHolder': string;
        'organizations.numberOfLicenses': string;
        'organizations.addressInformation': string;
        'organizations.streetAddress': string;
        'organizations.streetAddressPlaceholder': string;
        'organizations.city': string;
        'organizations.cityPlaceholder': string;
        'organizations.state': string;
        'organizations.statePlaceholder': string;
        'organizations.zipcode': string;
        'organizations.zipcodePlaceholder': string;
        'organizations.country': string;
        'organizations.countryPlaceholder': string;
        'organizations.description': string;
        'organizations.descriptionPlaceholder': string;
        'organizations.creating': string;
        'organizations.general': string;
        'organizations.2fa': string;
        'organizations.delete': string;
        'organizations.deleteMessage': string;
        'organizations.deleteConfirm': string;
        'license.title': string;
        'license.subtitle': string;
        'license.appsSelected': string;
        'license.totalLicenses': string;
        'license.numOfLicenses': string;
        'license.selectedApps': string;
        'license.noPermission': string;
        'license.availableLicenses': string;
        'license.monthlyCost': string;
        'license.applicationLicenses': string;
        'userMgmt.title': string;
        'userMgmt.addUser': string;
        'userMgmt.percentLicensesUsed': string;
        'userMgmt.exportUserList': string;
        'userMgmt.bulkUserOperations': string;
        'userMgmt.userAccessReport': string;
        '2fa.multiAuth': string;
        '2fa.enable2FA': string;
        '2fa.authenticationMethods': string;
        'billing.billingActions': string;
        'billing.generateInvoice': string;
        'billing.paymentHistory': string;
        'billing.updateBillingInfo': string;
        'personnel.title': string;
        'personnel.subtitle': string;
        'personnel.search': string;
        'personnel.error': string;
        'personnel.retry': string;
        'personnel.addPersonnel': string;
        'personnel.noPermission': string;
        'personnel.allOrganizations': string;
        'personnel.firstName': string;
        'personnel.lastName': string;
        'personnel.email': string;
        'personnel.phoneNumber': string;
        'personnel.organization': string;
        'personnel.roleSingle': string;
        'personnel.created': string;
        'personnel.updated': string;
        'personnel.edit': string;
        'personnel.noPersonnelFound': string;
        'personnel.autoSelectedFor': string;
        'personnel.selected': string;
        'personnel.oneRoleAllowed': string;
        'personnel.applications': string;
        'personnel.noApplicationsAvailable': string;
        'personnel.selectPermissions': string;
        'personnel.saving': string;
        'personnel.adding': string;
        'personnel.roleSelectOne': string;
        'personnel.roleSelectMultiple': string;
        'personnel.addNewPersonnel': string;
        'personnel.editPersonnel': string;
        'personnel.delete': string;
        'personnel.deleteMessage': string;
        'personnel.deleteConfirm': string;
        'personnel.changePassword': string;
        'personnel.passwordPlaceholder': string;
        'personnel.confirmPasswordPlaceholder': string;
        'roles.title': string;
        'roles.error': string;
        'roles.loading': string;
        'roles.create': string;
        'roles.edit': string;
        'roles.update': string;
        'roles.roleName': string;
        'roles.roleNamePlaceHolder': string;
        'roles.description': string;
        'roles.descriptionPlaceholder': string;
        'roles.permissions': string;
        'roles.personnel': string;
        'roles.organization': string;
        'roles.hierarchy': string;
        'roles.position': string;
        'roles.analytics': string;
        'roles.billing': string;
        'roles.schedule': string;
        'roles.shift': string;
        'roles.attendaceTracking': string;
        'roles.attendanceReports': string;
        'roles.report': string;
        'roles.alertRule': string;
        'roles.alertAndReportHistory': string;
        'roles.noRolesAvailable': string;
        'roles.delete': string;
        'roles.deleteMessage': string;
        'roles.deleteConfirm': string;
        'home.title': string;
        'home.welcome': string;
        'home.chooseApp': string;
        'home.searchPlaceholder': string;
        'home.noAppsFound': string;
        'home.open': string;
        'home.new': string;
        'home.recent': string;
        'home.list': string;
        'home.grid': string;
        'home.admin': string;
        'home.application': string;
        'org.title': string;
        'org.subtitle': string;
        'org.licenseManagement': string;
        'org.personnel': string;
        'org.organizationInfo': string;
        'org.analytics': string;
        'org.reportsAlerts': string;
        'org.hierarchyRoles': string;
        'org.scheduleAttendance': string;
        'org.back': string;
        'org.noPermission': string;
        'org.organizationInformation': string;
        'org.organizationSubInformation': string;
        'org.basicInformation': string;
        'org.contactInformation': string;
        'org.headquartersAddress': string;
        'org.streetAddress': string;
        'org.stateProvince': string;
        'org.zipPostalCode': string;
        'org.legalInformation': string;
        'org.accessPortal': string;
        'org.orgAdminPortal': string;
        'org.subheading': string;
        'org.enterpiseGradeManagement': string;
        'org.organizationManagementPortal': string;
        'org.organizationManagementPortalInfo': string;
        'org.launchAdminPortal': string;
        'org.viewDocumentation': string;
        'org.comprehensiveManagementCapabilities': string;
        'org.launchPortalNow': string;
        'org.readyToGetStarted': string;
        'analytics.title': string;
        'analytics.subtitle': string;
        'analytics.noPermission': string;
        'analytics.last7days': string;
        'analytics.last30days': string;
        'analytics.last90days': string;
        'analytics.lastYear': string;
        'analytics.fromLastMonth': string;
        'analytics.tokenUsage': string;
        'analytics.licenseCost': string;
        'analytics.averageSessionTime': string;
        'analytics.licenseUsageTrends': string;
        'analytics.userActivity': string;
        'analytics.departmentUsageDistribution': string;
        'analytics.costVsBudgetTrend': string;
        'analytics.usageInsights': string;
        'analytics.topPerformingApplications': string;
        'analytics.peakUsageTimes': string;
        'analytics.optimizationOpportunities': string;
        'reports.title': string;
        'reports.subtitle': string;
        'reports.newAlertRule': string;
        'reports.newReport': string;
        'reports.createAlertRule': string;
        'reports.createNewReport': string;
        'reports.createReport': string;
        'reports.alertName': string;
        'reports.triggerCondition': string;
        'reports.threshold': string;
        'reports.reportName': string;
        'reports.reportType': string;
        'reports.schedule': string;
        'reports.recipients': string;
        'reports.activeAlerts': string;
        'reports.scheduledReports': string;
        'reports.history': string;
        'reports.highPriority': string;
        'reports.resolvedToday': string;
        'reports.systemAlerts': string;
        'reports.noPermissionActiveAlerts': string;
        'reports.download': string;
        'reports.lastGenerated': string;
        'reports.format': string;
        'reports.people': string;
        'reports.noPermissionScheduledReports': string;
        'reports.alertAndReportHistory': string;
        'reports.historicalAlerts': string;
        'reports.noPermissionHistory': string;
        'orgHierarchy.title': string;
        'orgHierarchy.subtitle': string;
        'orgHierarchy.organizationalHierarchy': string;
        'orgHierarchy.rolesAndPermissions': string;
        'orgHierarchy.positionAndAssignments': string;
        'orgHierarchy.noPermissionPositionAssignment': string;
        'orgHierarchy.positionsFilled': string;
        'orgHierarchy.vacantPosition': string;
        'orgHierarchy.totalDepartments': string;
        'orgHierarchy.totalPositions': string;
        'orgHierarchy.filledPositions': string;
        'orgHierarchy.fillRate': string;
        'orgHierarchy.departmentStructure': string;
        'orgHierarchy.addDepartment': string;
        'orgHierarchy.editDepartment': string;
        'orgHierarchy.deleteDepartment': string;
        'orgHierarchy.deleteDepartmentDescription': string;
        'orgHierarchy.departmentName': string;
        'orgHierarchy.parentDepartment': string;
        'orgHierarchy.totalRoles': string;
        'orgHierarchy.customRoles': string;
        'orgHierarchy.adminUsers': string;
        'orgHierarchy.addPosition': string;
        'orgHierarchy.positionTitle': string;
        'orgHierarchy.department': string;
        'orgHierarchy.assignedPersonnel': string;
        'orgHierarchy.actions': string;
        'orgHierarchy.unassigned': string;
        'orgHierarchy.unassign.title': string;
        'orgHierarchy.unassign.description': string;
        'orgHierarchy.unassign.confirmText': string;
        'orgHierarchy.position.addPosition': string;
        'orgHierarchy.position.selectRoles': string;
        'scheduleAttendace.title': string;
        'scheduleAttendace.subtitle': string;
        'scheduleAttendace.createShift': string;
        'scheduleAttendace.createNewShift': string;
        'scheduleAttendace.scheduleEmployee': string;
        'scheduleAttendace.shiftName': string;
        'scheduleAttendace.startTime': string;
        'scheduleAttendace.endTime': string;
        'scheduleAttendace.capacity': string;
        'scheduleAttendace.employee': string;
        'scheduleAttendace.date': string;
        'scheduleAttendace.recurrence': string;
        'scheduleAttendace.totalScheduled': string;
        'scheduleAttendace.present': string;
        'scheduleAttendace.absent': string;
        'scheduleAttendace.attendaceRate': string;
        'scheduleAttendace.todaysSchedule': string;
        'scheduleAttendace.shiftManagement': string;
        'scheduleAttendace.attendanceTracking': string;
        'scheduleAttendace.attendanceReports': string;
        'scheduleAttendace.dailySchedule': string;
        'scheduleAttendace.checkIn': string;
        'scheduleAttendace.checkOut': string;
        'scheduleAttendace.noPermission': string;
        'scheduleAttendace.assigned': string;
        'scheduleAttendace.utilization': string;
        'scheduleAttendace.noPermissionShifts': string;
        'scheduleAttendace.attendanceTrackingTitle': string;
        'scheduleAttendace.noPermissionAttendanceTracking': string;
        'scheduleAttendace.attendanceReportsTitle': string;
        'scheduleAttendace.attendanceReportsSubTitle': string;
        'scheduleAttendace.noPermissionAttendanceReports': string;
    };
    es: {
        'header.title': string;
        'language.select': string;
        'language.current': string;
        'theme.select': string;
        'theme.light': string;
        'theme.dark': string;
        'theme.system': string;
        'common.save': string;
        'common.cancel': string;
        'common.apply': string;
        'common.close': string;
        'common.settings': string;
        'common.preferences': string;
        'common.loading': string;
        'common.retry': string;
        'common.error': string;
        'nav.home': string;
        'nav.about': string;
        'nav.contact': string;
        'nav.help': string;
        'nav.dashboard': string;
        'nav.personnel': string;
        'nav.organizations': string;
        'nav.roles': string;
        'dashboard.title': string;
        'dashboard.subtitle': string;
        'dashboard.totalOrgs': string;
        'dashboard.activeUsers': string;
        'dashboard.licenseRevenue': string;
        'dashboard.growthRate': string;
        'dashboard.recentOrgs': string;
        'dashboard.licenseUsage': string;
        'dashboard.licensesSold': string;
        'dashboard.utilizationRate': string;
        'dashboard.noRecentOrgs': string;
        'dashboard.loading': string;
        'dashboard.error': string;
        'dashboard.noData': string;
        'organizations.title': string;
        'organizations.subtitle': string;
        'organizations.addOrganization': string;
        'organizations.addOrganizations': string;
        'organizations.search': string;
        'organizations.users': string;
        'organizations.month': string;
        'organizations.viewDetails': string;
        'organizations.manage': string;
        'organizations.details': string;
        'organizations.orgId': string;
        'organizations.domain': string;
        'organizations.domainPlaceholder': string;
        'organizations.createdDate': string;
        'organizations.contactEmail': string;
        'organizations.lastLogin': string;
        'organizations.usageStats': string;
        'organizations.monthlyRevenue': string;
        'organizations.annualRevenue': string;
        'organizations.licenseUtilization': string;
        'organizations.license': string;
        'organizations.licenses': string;
        'organizations.current': string;
        'organizations.currentBillingPeriod': string;
        'organizations.currentUsage': string;
        'organizations.billingInformation': string;
        'organizations.billingCycle': string;
        'organizations.nextBillingDate': string;
        'organizations.paymentMethod': string;
        'organizations.totalLicenses': string;
        'organizations.status': string;
        'organizations.status.active': string;
        'organizations.status.pending': string;
        'organizations.status.suspended': string;
        'organizations.organizationName': string;
        'organizations.organizationNamePlaceHolder': string;
        'organizations.numberOfLicenses': string;
        'organizations.addressInformation': string;
        'organizations.streetAddress': string;
        'organizations.streetAddressPlaceholder': string;
        'organizations.city': string;
        'organizations.cityPlaceholder': string;
        'organizations.state': string;
        'organizations.statePlaceholder': string;
        'organizations.zipcode': string;
        'organizations.zipcodePlaceholder': string;
        'organizations.country': string;
        'organizations.countryPlaceholder': string;
        'organizations.description': string;
        'organizations.descriptionPlaceholder': string;
        'organizations.creating': string;
        'organizations.general': string;
        'organizations.2fa': string;
        'organizations.delete': string;
        'organizations.deleteMessage': string;
        'organizations.deleteConfirm': string;
        'license.title': string;
        'license.subtitle': string;
        'license.appsSelected': string;
        'license.totalLicenses': string;
        'license.numOfLicenses': string;
        'license.selectedApps': string;
        'license.noPermission': string;
        'license.availableLicenses': string;
        'license.monthlyCost': string;
        'license.applicationLicenses': string;
        'userMgmt.title': string;
        'userMgmt.addUser': string;
        'userMgmt.percentLicensesUsed': string;
        'userMgmt.exportUserList': string;
        'userMgmt.bulkUserOperations': string;
        'userMgmt.userAccessReport': string;
        'billing.billingActions': string;
        'billing.generateInvoice': string;
        'billing.paymentHistory': string;
        'billing.updateBillingInfo': string;
        '2fa.multiAuth': string;
        '2fa.enable2FA': string;
        '2fa.authenticationMethods': string;
        'personnel.title': string;
        'personnel.subtitle': string;
        'personnel.search': string;
        'personnel.error': string;
        'personnel.retry': string;
        'personnel.noPermission': string;
        'personnel.allOrganizations': string;
        'personnel.addPersonnel': string;
        'personnel.firstName': string;
        'personnel.lastName': string;
        'personnel.email': string;
        'personnel.phoneNumber': string;
        'personnel.organization': string;
        'personnel.roleSingle': string;
        'personnel.created': string;
        'personnel.updated': string;
        'personnel.edit': string;
        'personnel.noPersonnelFound': string;
        'personnel.autoSelectedFor': string;
        'personnel.selected': string;
        'personnel.oneRoleAllowed': string;
        'personnel.applications': string;
        'personnel.noApplicationsAvailable': string;
        'personnel.selectPermissions': string;
        'personnel.saving': string;
        'personnel.adding': string;
        'personnel.roleSelectOne': string;
        'personnel.roleSelectMultiple': string;
        'personnel.addNewPersonnel': string;
        'personnel.editPersonnel': string;
        'personnel.delete': string;
        'personnel.deleteMessage': string;
        'personnel.deleteConfirm': string;
        'personnel.changePassword': string;
        'personnel.passwordPlaceholder': string;
        'personnel.confirmPasswordPlaceholder': string;
        'roles.title': string;
        'roles.error': string;
        'roles.loading': string;
        'roles.create': string;
        'roles.edit': string;
        'roles.update': string;
        'roles.roleName': string;
        'roles.roleNamePlaceholder': string;
        'roles.description': string;
        'roles.descriptionPlaceholder': string;
        'roles.permissions': string;
        'roles.personnel': string;
        'roles.organization': string;
        'roles.hierarchy': string;
        'roles.position': string;
        'roles.analytics': string;
        'roles.billing': string;
        'roles.schedule': string;
        'roles.shift': string;
        'roles.attendaceTracking': string;
        'roles.attendanceReports': string;
        'roles.report': string;
        'roles.alertRule': string;
        'roles.alertAndReportHistory': string;
        'roles.noRolesAvailable': string;
        'roles.delete': string;
        'roles.deleteMessage': string;
        'roles.deleteConfirm': string;
        'home.title': string;
        'home.welcome': string;
        'home.chooseApp': string;
        'home.searchPlaceholder': string;
        'home.noAppsFound': string;
        'home.open': string;
        'home.new': string;
        'home.recent': string;
        'home.list': string;
        'home.grid': string;
        'home.admin': string;
        'home.application': string;
        'org.title': string;
        'org.subtitle': string;
        'org.licenseManagement': string;
        'org.personnel': string;
        'org.organizationInfo': string;
        'org.analytics': string;
        'org.reportsAlerts': string;
        'org.hierarchyRoles': string;
        'org.scheduleAttendance': string;
        'org.back': string;
        'org.noPermission': string;
        'org.organizationInformation': string;
        'org.organizationSubInformation': string;
        'org.basicInformation': string;
        'org.contactInformation': string;
        'org.headquartersAddress': string;
        'org.streetAddress': string;
        'org.stateProvince': string;
        'org.zipPostalCode': string;
        'org.legalInformation': string;
        'org.accessPortal': string;
        'org.orgAdminPortal': string;
        'org.subheading': string;
        'org.enterpiseGradeManagement': string;
        'org.organizationManagementPortal': string;
        'org.organizationManagementPortalInfo': string;
        'org.launchAdminPortal': string;
        'org.viewDocumentation': string;
        'org.comprehensiveManagementCapabilities': string;
        'org.launchPortalNow': string;
        'org.readyToGetStarted': string;
        'analytics.title': string;
        'analytics.subtitle': string;
        'analytics.noPermission': string;
        'analytics.last7days': string;
        'analytics.last30days': string;
        'analytics.last90days': string;
        'analytics.lastYear': string;
        'analytics.fromLastMonth': string;
        'analytics.tokenUsage': string;
        'analytics.licenseCost': string;
        'analytics.averageSessionTime': string;
        'analytics.licenseUsageTrends': string;
        'analytics.userActivity': string;
        'analytics.departmentUsageDistribution': string;
        'analytics.costVsBudgetTrend': string;
        'analytics.usageInsights': string;
        'analytics.topPerformingApplications': string;
        'analytics.peakUsageTimes': string;
        'analytics.optimizationOpportunities': string;
        'reports.title': string;
        'reports.subtitle': string;
        'reports.newAlertRule': string;
        'reports.newReport': string;
        'reports.createAlertRule': string;
        'reports.createNewReport': string;
        'reports.createReport': string;
        'reports.alertName': string;
        'reports.triggerCondition': string;
        'reports.threshold': string;
        'reports.reportName': string;
        'reports.reportType': string;
        'reports.schedule': string;
        'reports.recipients': string;
        'reports.activeAlerts': string;
        'reports.scheduledReports': string;
        'reports.history': string;
        'reports.highPriority': string;
        'reports.resolvedToday': string;
        'reports.systemAlerts': string;
        'reports.noPermissionActiveAlerts': string;
        'reports.download': string;
        'reports.lastGenerated': string;
        'reports.format': string;
        'reports.people': string;
        'reports.noPermissionScheduledReports': string;
        'reports.alertAndReportHistory': string;
        'reports.historicalAlerts': string;
        'reports.noPermissionHistory': string;
        'orgHierarchy.title': string;
        'orgHierarchy.subtitle': string;
        'orgHierarchy.organizationalHierarchy': string;
        'orgHierarchy.rolesAndPermissions': string;
        'orgHierarchy.positionAndAssignments': string;
        'orgHierarchy.noPermissionPositionAssignment': string;
        'orgHierarchy.positionsFilled': string;
        'orgHierarchy.vacantPosition': string;
        'orgHierarchy.totalDepartments': string;
        'orgHierarchy.totalPositions': string;
        'orgHierarchy.filledPositions': string;
        'orgHierarchy.fillRate': string;
        'orgHierarchy.departmentStructure': string;
        'orgHierarchy.addDepartment': string;
        'orgHierarchy.editDepartment': string;
        'orgHierarchy.deleteDepartment': string;
        'orgHierarchy.deleteDepartmentDescription': string;
        'orgHierarchy.departmentName': string;
        'orgHierarchy.parentDepartment': string;
        'orgHierarchy.totalRoles': string;
        'orgHierarchy.customRoles': string;
        'orgHierarchy.adminUsers': string;
        'orgHierarchy.addPosition': string;
        'orgHierarchy.positionTitle': string;
        'orgHierarchy.department': string;
        'orgHierarchy.assignedPersonnel': string;
        'orgHierarchy.actions': string;
        'orgHierarchy.unassigned': string;
        'orgHierarchy.unassign.title': string;
        'orgHierarchy.unassign.description': string;
        'orgHierarchy.unassign.confirmText': string;
        'orgHierarchy.position.addPosition': string;
        'orgHierarchy.position.selectRoles': string;
        'scheduleAttendace.title': string;
        'scheduleAttendace.subtitle': string;
        'scheduleAttendace.createShift': string;
        'scheduleAttendace.createNewShift': string;
        'scheduleAttendace.scheduleEmployee': string;
        'scheduleAttendace.shiftName': string;
        'scheduleAttendace.startTime': string;
        'scheduleAttendace.endTime': string;
        'scheduleAttendace.capacity': string;
        'scheduleAttendace.employee': string;
        'scheduleAttendace.date': string;
        'scheduleAttendace.recurrence': string;
        'scheduleAttendace.totalScheduled': string;
        'scheduleAttendace.present': string;
        'scheduleAttendace.absent': string;
        'scheduleAttendace.attendaceRate': string;
        'scheduleAttendace.todaysSchedule': string;
        'scheduleAttendace.shiftManagement': string;
        'scheduleAttendace.attendanceTracking': string;
        'scheduleAttendace.attendanceReports': string;
        'scheduleAttendace.dailySchedule': string;
        'scheduleAttendace.checkIn': string;
        'scheduleAttendace.checkOut': string;
        'scheduleAttendace.noPermission': string;
        'scheduleAttendace.assigned': string;
        'scheduleAttendace.utilization': string;
        'scheduleAttendace.noPermissionShifts': string;
        'scheduleAttendace.attendanceTrackingTitle': string;
        'scheduleAttendace.noPermissionAttendanceTracking': string;
        'scheduleAttendace.attendanceReportsTitle': string;
        'scheduleAttendace.attendanceReportsSubTitle': string;
        'scheduleAttendace.noPermissionAttendanceReports': string;
    };
};
declare const _default: {
    languages: Language[];
    createLanguageConfig: typeof createLanguageConfig;
    commonTranslations: {
        en: {
            'header.title': string;
            'language.select': string;
            'language.current': string;
            'theme.select': string;
            'theme.light': string;
            'theme.dark': string;
            'theme.system': string;
            'common.save': string;
            'common.cancel': string;
            'common.apply': string;
            'common.close': string;
            'common.settings': string;
            'common.preferences': string;
            'common.loading': string;
            'common.retry': string;
            'common.error': string;
            'nav.home': string;
            'nav.about': string;
            'nav.contact': string;
            'nav.help': string;
            'nav.dashboard': string;
            'nav.personnel': string;
            'nav.organizations': string;
            'nav.roles': string;
            'dashboard.title': string;
            'dashboard.subtitle': string;
            'dashboard.totalOrgs': string;
            'dashboard.activeUsers': string;
            'dashboard.licenseRevenue': string;
            'dashboard.growthRate': string;
            'dashboard.recentOrgs': string;
            'dashboard.licenseUsage': string;
            'dashboard.licensesSold': string;
            'dashboard.utilizationRate': string;
            'dashboard.noRecentOrgs': string;
            'dashboard.loading': string;
            'dashboard.noData': string;
            'dashboard.error': string;
            'organizations.title': string;
            'organizations.subtitle': string;
            'organizations.addOrganization': string;
            'organizations.addOrganizations': string;
            'organizations.search': string;
            'organizations.users': string;
            'organizations.month': string;
            'organizations.viewDetails': string;
            'organizations.manage': string;
            'organizations.details': string;
            'organizations.orgId': string;
            'organizations.domain': string;
            'organizations.domainPlaceholder': string;
            'organizations.createdDate': string;
            'organizations.contactEmail': string;
            'organizations.lastLogin': string;
            'organizations.usageStats': string;
            'organizations.monthlyRevenue': string;
            'organizations.annualRevenue': string;
            'organizations.licenseUtilization': string;
            'organizations.license': string;
            'organizations.licenses': string;
            'organizations.current': string;
            'organizations.currentBillingPeriod': string;
            'organizations.currentUsage': string;
            'organizations.billingInformation': string;
            'organizations.billingCycle': string;
            'organizations.nextBillingDate': string;
            'organizations.paymentMethod': string;
            'organizations.totalLicenses': string;
            'organizations.status': string;
            'organizations.status.active': string;
            'organizations.status.pending': string;
            'organizations.status.suspended': string;
            'organizations.organizationName': string;
            'organizations.organizationNamePlaceHolder': string;
            'organizations.numberOfLicenses': string;
            'organizations.addressInformation': string;
            'organizations.streetAddress': string;
            'organizations.streetAddressPlaceholder': string;
            'organizations.city': string;
            'organizations.cityPlaceholder': string;
            'organizations.state': string;
            'organizations.statePlaceholder': string;
            'organizations.zipcode': string;
            'organizations.zipcodePlaceholder': string;
            'organizations.country': string;
            'organizations.countryPlaceholder': string;
            'organizations.description': string;
            'organizations.descriptionPlaceholder': string;
            'organizations.creating': string;
            'organizations.general': string;
            'organizations.2fa': string;
            'organizations.delete': string;
            'organizations.deleteMessage': string;
            'organizations.deleteConfirm': string;
            'license.title': string;
            'license.subtitle': string;
            'license.appsSelected': string;
            'license.totalLicenses': string;
            'license.numOfLicenses': string;
            'license.selectedApps': string;
            'license.noPermission': string;
            'license.availableLicenses': string;
            'license.monthlyCost': string;
            'license.applicationLicenses': string;
            'userMgmt.title': string;
            'userMgmt.addUser': string;
            'userMgmt.percentLicensesUsed': string;
            'userMgmt.exportUserList': string;
            'userMgmt.bulkUserOperations': string;
            'userMgmt.userAccessReport': string;
            '2fa.multiAuth': string;
            '2fa.enable2FA': string;
            '2fa.authenticationMethods': string;
            'billing.billingActions': string;
            'billing.generateInvoice': string;
            'billing.paymentHistory': string;
            'billing.updateBillingInfo': string;
            'personnel.title': string;
            'personnel.subtitle': string;
            'personnel.search': string;
            'personnel.error': string;
            'personnel.retry': string;
            'personnel.addPersonnel': string;
            'personnel.noPermission': string;
            'personnel.allOrganizations': string;
            'personnel.firstName': string;
            'personnel.lastName': string;
            'personnel.email': string;
            'personnel.phoneNumber': string;
            'personnel.organization': string;
            'personnel.roleSingle': string;
            'personnel.created': string;
            'personnel.updated': string;
            'personnel.edit': string;
            'personnel.noPersonnelFound': string;
            'personnel.autoSelectedFor': string;
            'personnel.selected': string;
            'personnel.oneRoleAllowed': string;
            'personnel.applications': string;
            'personnel.noApplicationsAvailable': string;
            'personnel.selectPermissions': string;
            'personnel.saving': string;
            'personnel.adding': string;
            'personnel.roleSelectOne': string;
            'personnel.roleSelectMultiple': string;
            'personnel.addNewPersonnel': string;
            'personnel.editPersonnel': string;
            'personnel.delete': string;
            'personnel.deleteMessage': string;
            'personnel.deleteConfirm': string;
            'personnel.changePassword': string;
            'personnel.passwordPlaceholder': string;
            'personnel.confirmPasswordPlaceholder': string;
            'roles.title': string;
            'roles.error': string;
            'roles.loading': string;
            'roles.create': string;
            'roles.edit': string;
            'roles.update': string;
            'roles.roleName': string;
            'roles.roleNamePlaceHolder': string;
            'roles.description': string;
            'roles.descriptionPlaceholder': string;
            'roles.permissions': string;
            'roles.personnel': string;
            'roles.organization': string;
            'roles.hierarchy': string;
            'roles.position': string;
            'roles.analytics': string;
            'roles.billing': string;
            'roles.schedule': string;
            'roles.shift': string;
            'roles.attendaceTracking': string;
            'roles.attendanceReports': string;
            'roles.report': string;
            'roles.alertRule': string;
            'roles.alertAndReportHistory': string;
            'roles.noRolesAvailable': string;
            'roles.delete': string;
            'roles.deleteMessage': string;
            'roles.deleteConfirm': string;
            'home.title': string;
            'home.welcome': string;
            'home.chooseApp': string;
            'home.searchPlaceholder': string;
            'home.noAppsFound': string;
            'home.open': string;
            'home.new': string;
            'home.recent': string;
            'home.list': string;
            'home.grid': string;
            'home.admin': string;
            'home.application': string;
            'org.title': string;
            'org.subtitle': string;
            'org.licenseManagement': string;
            'org.personnel': string;
            'org.organizationInfo': string;
            'org.analytics': string;
            'org.reportsAlerts': string;
            'org.hierarchyRoles': string;
            'org.scheduleAttendance': string;
            'org.back': string;
            'org.noPermission': string;
            'org.organizationInformation': string;
            'org.organizationSubInformation': string;
            'org.basicInformation': string;
            'org.contactInformation': string;
            'org.headquartersAddress': string;
            'org.streetAddress': string;
            'org.stateProvince': string;
            'org.zipPostalCode': string;
            'org.legalInformation': string;
            'org.accessPortal': string;
            'org.orgAdminPortal': string;
            'org.subheading': string;
            'org.enterpiseGradeManagement': string;
            'org.organizationManagementPortal': string;
            'org.organizationManagementPortalInfo': string;
            'org.launchAdminPortal': string;
            'org.viewDocumentation': string;
            'org.comprehensiveManagementCapabilities': string;
            'org.launchPortalNow': string;
            'org.readyToGetStarted': string;
            'analytics.title': string;
            'analytics.subtitle': string;
            'analytics.noPermission': string;
            'analytics.last7days': string;
            'analytics.last30days': string;
            'analytics.last90days': string;
            'analytics.lastYear': string;
            'analytics.fromLastMonth': string;
            'analytics.tokenUsage': string;
            'analytics.licenseCost': string;
            'analytics.averageSessionTime': string;
            'analytics.licenseUsageTrends': string;
            'analytics.userActivity': string;
            'analytics.departmentUsageDistribution': string;
            'analytics.costVsBudgetTrend': string;
            'analytics.usageInsights': string;
            'analytics.topPerformingApplications': string;
            'analytics.peakUsageTimes': string;
            'analytics.optimizationOpportunities': string;
            'reports.title': string;
            'reports.subtitle': string;
            'reports.newAlertRule': string;
            'reports.newReport': string;
            'reports.createAlertRule': string;
            'reports.createNewReport': string;
            'reports.createReport': string;
            'reports.alertName': string;
            'reports.triggerCondition': string;
            'reports.threshold': string;
            'reports.reportName': string;
            'reports.reportType': string;
            'reports.schedule': string;
            'reports.recipients': string;
            'reports.activeAlerts': string;
            'reports.scheduledReports': string;
            'reports.history': string;
            'reports.highPriority': string;
            'reports.resolvedToday': string;
            'reports.systemAlerts': string;
            'reports.noPermissionActiveAlerts': string;
            'reports.download': string;
            'reports.lastGenerated': string;
            'reports.format': string;
            'reports.people': string;
            'reports.noPermissionScheduledReports': string;
            'reports.alertAndReportHistory': string;
            'reports.historicalAlerts': string;
            'reports.noPermissionHistory': string;
            'orgHierarchy.title': string;
            'orgHierarchy.subtitle': string;
            'orgHierarchy.organizationalHierarchy': string;
            'orgHierarchy.rolesAndPermissions': string;
            'orgHierarchy.positionAndAssignments': string;
            'orgHierarchy.noPermissionPositionAssignment': string;
            'orgHierarchy.positionsFilled': string;
            'orgHierarchy.vacantPosition': string;
            'orgHierarchy.totalDepartments': string;
            'orgHierarchy.totalPositions': string;
            'orgHierarchy.filledPositions': string;
            'orgHierarchy.fillRate': string;
            'orgHierarchy.departmentStructure': string;
            'orgHierarchy.addDepartment': string;
            'orgHierarchy.editDepartment': string;
            'orgHierarchy.deleteDepartment': string;
            'orgHierarchy.deleteDepartmentDescription': string;
            'orgHierarchy.departmentName': string;
            'orgHierarchy.parentDepartment': string;
            'orgHierarchy.totalRoles': string;
            'orgHierarchy.customRoles': string;
            'orgHierarchy.adminUsers': string;
            'orgHierarchy.addPosition': string;
            'orgHierarchy.positionTitle': string;
            'orgHierarchy.department': string;
            'orgHierarchy.assignedPersonnel': string;
            'orgHierarchy.actions': string;
            'orgHierarchy.unassigned': string;
            'orgHierarchy.unassign.title': string;
            'orgHierarchy.unassign.description': string;
            'orgHierarchy.unassign.confirmText': string;
            'orgHierarchy.position.addPosition': string;
            'orgHierarchy.position.selectRoles': string;
            'scheduleAttendace.title': string;
            'scheduleAttendace.subtitle': string;
            'scheduleAttendace.createShift': string;
            'scheduleAttendace.createNewShift': string;
            'scheduleAttendace.scheduleEmployee': string;
            'scheduleAttendace.shiftName': string;
            'scheduleAttendace.startTime': string;
            'scheduleAttendace.endTime': string;
            'scheduleAttendace.capacity': string;
            'scheduleAttendace.employee': string;
            'scheduleAttendace.date': string;
            'scheduleAttendace.recurrence': string;
            'scheduleAttendace.totalScheduled': string;
            'scheduleAttendace.present': string;
            'scheduleAttendace.absent': string;
            'scheduleAttendace.attendaceRate': string;
            'scheduleAttendace.todaysSchedule': string;
            'scheduleAttendace.shiftManagement': string;
            'scheduleAttendace.attendanceTracking': string;
            'scheduleAttendace.attendanceReports': string;
            'scheduleAttendace.dailySchedule': string;
            'scheduleAttendace.checkIn': string;
            'scheduleAttendace.checkOut': string;
            'scheduleAttendace.noPermission': string;
            'scheduleAttendace.assigned': string;
            'scheduleAttendace.utilization': string;
            'scheduleAttendace.noPermissionShifts': string;
            'scheduleAttendace.attendanceTrackingTitle': string;
            'scheduleAttendace.noPermissionAttendanceTracking': string;
            'scheduleAttendace.attendanceReportsTitle': string;
            'scheduleAttendace.attendanceReportsSubTitle': string;
            'scheduleAttendace.noPermissionAttendanceReports': string;
        };
        es: {
            'header.title': string;
            'language.select': string;
            'language.current': string;
            'theme.select': string;
            'theme.light': string;
            'theme.dark': string;
            'theme.system': string;
            'common.save': string;
            'common.cancel': string;
            'common.apply': string;
            'common.close': string;
            'common.settings': string;
            'common.preferences': string;
            'common.loading': string;
            'common.retry': string;
            'common.error': string;
            'nav.home': string;
            'nav.about': string;
            'nav.contact': string;
            'nav.help': string;
            'nav.dashboard': string;
            'nav.personnel': string;
            'nav.organizations': string;
            'nav.roles': string;
            'dashboard.title': string;
            'dashboard.subtitle': string;
            'dashboard.totalOrgs': string;
            'dashboard.activeUsers': string;
            'dashboard.licenseRevenue': string;
            'dashboard.growthRate': string;
            'dashboard.recentOrgs': string;
            'dashboard.licenseUsage': string;
            'dashboard.licensesSold': string;
            'dashboard.utilizationRate': string;
            'dashboard.noRecentOrgs': string;
            'dashboard.loading': string;
            'dashboard.error': string;
            'dashboard.noData': string;
            'organizations.title': string;
            'organizations.subtitle': string;
            'organizations.addOrganization': string;
            'organizations.addOrganizations': string;
            'organizations.search': string;
            'organizations.users': string;
            'organizations.month': string;
            'organizations.viewDetails': string;
            'organizations.manage': string;
            'organizations.details': string;
            'organizations.orgId': string;
            'organizations.domain': string;
            'organizations.domainPlaceholder': string;
            'organizations.createdDate': string;
            'organizations.contactEmail': string;
            'organizations.lastLogin': string;
            'organizations.usageStats': string;
            'organizations.monthlyRevenue': string;
            'organizations.annualRevenue': string;
            'organizations.licenseUtilization': string;
            'organizations.license': string;
            'organizations.licenses': string;
            'organizations.current': string;
            'organizations.currentBillingPeriod': string;
            'organizations.currentUsage': string;
            'organizations.billingInformation': string;
            'organizations.billingCycle': string;
            'organizations.nextBillingDate': string;
            'organizations.paymentMethod': string;
            'organizations.totalLicenses': string;
            'organizations.status': string;
            'organizations.status.active': string;
            'organizations.status.pending': string;
            'organizations.status.suspended': string;
            'organizations.organizationName': string;
            'organizations.organizationNamePlaceHolder': string;
            'organizations.numberOfLicenses': string;
            'organizations.addressInformation': string;
            'organizations.streetAddress': string;
            'organizations.streetAddressPlaceholder': string;
            'organizations.city': string;
            'organizations.cityPlaceholder': string;
            'organizations.state': string;
            'organizations.statePlaceholder': string;
            'organizations.zipcode': string;
            'organizations.zipcodePlaceholder': string;
            'organizations.country': string;
            'organizations.countryPlaceholder': string;
            'organizations.description': string;
            'organizations.descriptionPlaceholder': string;
            'organizations.creating': string;
            'organizations.general': string;
            'organizations.2fa': string;
            'organizations.delete': string;
            'organizations.deleteMessage': string;
            'organizations.deleteConfirm': string;
            'license.title': string;
            'license.subtitle': string;
            'license.appsSelected': string;
            'license.totalLicenses': string;
            'license.numOfLicenses': string;
            'license.selectedApps': string;
            'license.noPermission': string;
            'license.availableLicenses': string;
            'license.monthlyCost': string;
            'license.applicationLicenses': string;
            'userMgmt.title': string;
            'userMgmt.addUser': string;
            'userMgmt.percentLicensesUsed': string;
            'userMgmt.exportUserList': string;
            'userMgmt.bulkUserOperations': string;
            'userMgmt.userAccessReport': string;
            'billing.billingActions': string;
            'billing.generateInvoice': string;
            'billing.paymentHistory': string;
            'billing.updateBillingInfo': string;
            '2fa.multiAuth': string;
            '2fa.enable2FA': string;
            '2fa.authenticationMethods': string;
            'personnel.title': string;
            'personnel.subtitle': string;
            'personnel.search': string;
            'personnel.error': string;
            'personnel.retry': string;
            'personnel.noPermission': string;
            'personnel.allOrganizations': string;
            'personnel.addPersonnel': string;
            'personnel.firstName': string;
            'personnel.lastName': string;
            'personnel.email': string;
            'personnel.phoneNumber': string;
            'personnel.organization': string;
            'personnel.roleSingle': string;
            'personnel.created': string;
            'personnel.updated': string;
            'personnel.edit': string;
            'personnel.noPersonnelFound': string;
            'personnel.autoSelectedFor': string;
            'personnel.selected': string;
            'personnel.oneRoleAllowed': string;
            'personnel.applications': string;
            'personnel.noApplicationsAvailable': string;
            'personnel.selectPermissions': string;
            'personnel.saving': string;
            'personnel.adding': string;
            'personnel.roleSelectOne': string;
            'personnel.roleSelectMultiple': string;
            'personnel.addNewPersonnel': string;
            'personnel.editPersonnel': string;
            'personnel.delete': string;
            'personnel.deleteMessage': string;
            'personnel.deleteConfirm': string;
            'personnel.changePassword': string;
            'personnel.passwordPlaceholder': string;
            'personnel.confirmPasswordPlaceholder': string;
            'roles.title': string;
            'roles.error': string;
            'roles.loading': string;
            'roles.create': string;
            'roles.edit': string;
            'roles.update': string;
            'roles.roleName': string;
            'roles.roleNamePlaceholder': string;
            'roles.description': string;
            'roles.descriptionPlaceholder': string;
            'roles.permissions': string;
            'roles.personnel': string;
            'roles.organization': string;
            'roles.hierarchy': string;
            'roles.position': string;
            'roles.analytics': string;
            'roles.billing': string;
            'roles.schedule': string;
            'roles.shift': string;
            'roles.attendaceTracking': string;
            'roles.attendanceReports': string;
            'roles.report': string;
            'roles.alertRule': string;
            'roles.alertAndReportHistory': string;
            'roles.noRolesAvailable': string;
            'roles.delete': string;
            'roles.deleteMessage': string;
            'roles.deleteConfirm': string;
            'home.title': string;
            'home.welcome': string;
            'home.chooseApp': string;
            'home.searchPlaceholder': string;
            'home.noAppsFound': string;
            'home.open': string;
            'home.new': string;
            'home.recent': string;
            'home.list': string;
            'home.grid': string;
            'home.admin': string;
            'home.application': string;
            'org.title': string;
            'org.subtitle': string;
            'org.licenseManagement': string;
            'org.personnel': string;
            'org.organizationInfo': string;
            'org.analytics': string;
            'org.reportsAlerts': string;
            'org.hierarchyRoles': string;
            'org.scheduleAttendance': string;
            'org.back': string;
            'org.noPermission': string;
            'org.organizationInformation': string;
            'org.organizationSubInformation': string;
            'org.basicInformation': string;
            'org.contactInformation': string;
            'org.headquartersAddress': string;
            'org.streetAddress': string;
            'org.stateProvince': string;
            'org.zipPostalCode': string;
            'org.legalInformation': string;
            'org.accessPortal': string;
            'org.orgAdminPortal': string;
            'org.subheading': string;
            'org.enterpiseGradeManagement': string;
            'org.organizationManagementPortal': string;
            'org.organizationManagementPortalInfo': string;
            'org.launchAdminPortal': string;
            'org.viewDocumentation': string;
            'org.comprehensiveManagementCapabilities': string;
            'org.launchPortalNow': string;
            'org.readyToGetStarted': string;
            'analytics.title': string;
            'analytics.subtitle': string;
            'analytics.noPermission': string;
            'analytics.last7days': string;
            'analytics.last30days': string;
            'analytics.last90days': string;
            'analytics.lastYear': string;
            'analytics.fromLastMonth': string;
            'analytics.tokenUsage': string;
            'analytics.licenseCost': string;
            'analytics.averageSessionTime': string;
            'analytics.licenseUsageTrends': string;
            'analytics.userActivity': string;
            'analytics.departmentUsageDistribution': string;
            'analytics.costVsBudgetTrend': string;
            'analytics.usageInsights': string;
            'analytics.topPerformingApplications': string;
            'analytics.peakUsageTimes': string;
            'analytics.optimizationOpportunities': string;
            'reports.title': string;
            'reports.subtitle': string;
            'reports.newAlertRule': string;
            'reports.newReport': string;
            'reports.createAlertRule': string;
            'reports.createNewReport': string;
            'reports.createReport': string;
            'reports.alertName': string;
            'reports.triggerCondition': string;
            'reports.threshold': string;
            'reports.reportName': string;
            'reports.reportType': string;
            'reports.schedule': string;
            'reports.recipients': string;
            'reports.activeAlerts': string;
            'reports.scheduledReports': string;
            'reports.history': string;
            'reports.highPriority': string;
            'reports.resolvedToday': string;
            'reports.systemAlerts': string;
            'reports.noPermissionActiveAlerts': string;
            'reports.download': string;
            'reports.lastGenerated': string;
            'reports.format': string;
            'reports.people': string;
            'reports.noPermissionScheduledReports': string;
            'reports.alertAndReportHistory': string;
            'reports.historicalAlerts': string;
            'reports.noPermissionHistory': string;
            'orgHierarchy.title': string;
            'orgHierarchy.subtitle': string;
            'orgHierarchy.organizationalHierarchy': string;
            'orgHierarchy.rolesAndPermissions': string;
            'orgHierarchy.positionAndAssignments': string;
            'orgHierarchy.noPermissionPositionAssignment': string;
            'orgHierarchy.positionsFilled': string;
            'orgHierarchy.vacantPosition': string;
            'orgHierarchy.totalDepartments': string;
            'orgHierarchy.totalPositions': string;
            'orgHierarchy.filledPositions': string;
            'orgHierarchy.fillRate': string;
            'orgHierarchy.departmentStructure': string;
            'orgHierarchy.addDepartment': string;
            'orgHierarchy.editDepartment': string;
            'orgHierarchy.deleteDepartment': string;
            'orgHierarchy.deleteDepartmentDescription': string;
            'orgHierarchy.departmentName': string;
            'orgHierarchy.parentDepartment': string;
            'orgHierarchy.totalRoles': string;
            'orgHierarchy.customRoles': string;
            'orgHierarchy.adminUsers': string;
            'orgHierarchy.addPosition': string;
            'orgHierarchy.positionTitle': string;
            'orgHierarchy.department': string;
            'orgHierarchy.assignedPersonnel': string;
            'orgHierarchy.actions': string;
            'orgHierarchy.unassigned': string;
            'orgHierarchy.unassign.title': string;
            'orgHierarchy.unassign.description': string;
            'orgHierarchy.unassign.confirmText': string;
            'orgHierarchy.position.addPosition': string;
            'orgHierarchy.position.selectRoles': string;
            'scheduleAttendace.title': string;
            'scheduleAttendace.subtitle': string;
            'scheduleAttendace.createShift': string;
            'scheduleAttendace.createNewShift': string;
            'scheduleAttendace.scheduleEmployee': string;
            'scheduleAttendace.shiftName': string;
            'scheduleAttendace.startTime': string;
            'scheduleAttendace.endTime': string;
            'scheduleAttendace.capacity': string;
            'scheduleAttendace.employee': string;
            'scheduleAttendace.date': string;
            'scheduleAttendace.recurrence': string;
            'scheduleAttendace.totalScheduled': string;
            'scheduleAttendace.present': string;
            'scheduleAttendace.absent': string;
            'scheduleAttendace.attendaceRate': string;
            'scheduleAttendace.todaysSchedule': string;
            'scheduleAttendace.shiftManagement': string;
            'scheduleAttendace.attendanceTracking': string;
            'scheduleAttendace.attendanceReports': string;
            'scheduleAttendace.dailySchedule': string;
            'scheduleAttendace.checkIn': string;
            'scheduleAttendace.checkOut': string;
            'scheduleAttendace.noPermission': string;
            'scheduleAttendace.assigned': string;
            'scheduleAttendace.utilization': string;
            'scheduleAttendace.noPermissionShifts': string;
            'scheduleAttendace.attendanceTrackingTitle': string;
            'scheduleAttendace.noPermissionAttendanceTracking': string;
            'scheduleAttendace.attendanceReportsTitle': string;
            'scheduleAttendace.attendanceReportsSubTitle': string;
            'scheduleAttendace.noPermissionAttendanceReports': string;
        };
    };
};
export default _default;
