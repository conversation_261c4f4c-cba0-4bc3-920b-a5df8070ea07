import { type LanguageConfig, type Language } from './LanguageProvider';
export declare const languages: Language[];
export declare const reactLanguageConfig: LanguageConfig;
export declare function createLanguageConfig(overrides?: Partial<LanguageConfig>): LanguageConfig;
export declare const commonTranslations: {
    en: {
        'header.title': string;
        'language.select': string;
        'language.current': string;
        'theme.select': string;
        'theme.light': string;
        'theme.dark': string;
        'theme.system': string;
        'common.save': string;
        'common.cancel': string;
        'common.apply': string;
        'common.close': string;
        'common.settings': string;
        'common.preferences': string;
        'nav.home': string;
        'nav.about': string;
        'nav.contact': string;
        'nav.help': string;
        'nav.dashboard': string;
        'nav.personnel': string;
        'nav.organizations': string;
        'nav.roles': string;
        'dashboard.title': string;
        'dashboard.subtitle': string;
        'dashboard.totalOrgs': string;
        'dashboard.activeUsers': string;
        'dashboard.licenseRevenue': string;
        'dashboard.growthRate': string;
        'dashboard.recentOrgs': string;
        'dashboard.licenseUsage': string;
        'dashboard.licensesSold': string;
        'dashboard.utilizationRate': string;
        'dashboard.noRecentOrgs': string;
        'dashboard.loading': string;
        'dashboard.noData': string;
        'dashboard.error': string;
        'organizations.title': string;
        'organizations.subtitle': string;
        'organizations.addOrganization': string;
        'organizations.addOrganizations': string;
        'organizations.search': string;
        'organizations.users': string;
        'organizations.month': string;
        'organizations.viewDetails': string;
        'organizations.manage': string;
        'organizations.details': string;
        'organizations.orgId': string;
        'organizations.domain': string;
        'organizations.domainPlaceholder': string;
        'organizations.createdDate': string;
        'organizations.contactEmail': string;
        'organizations.lastLogin': string;
        'organizations.usageStats': string;
        'organizations.monthlyRevenue': string;
        'organizations.annualRevenue': string;
        'organizations.licenseUtilization': string;
        'organizations.license': string;
        'organizations.licenses': string;
        'organizations.current': string;
        'organizations.currentBillingPeriod': string;
        'organizations.currentUsage': string;
        'organizations.billingInformation': string;
        'organizations.billingCycle': string;
        'organizations.nextBillingDate': string;
        'organizations.paymentMethod': string;
        'organizations.totalLicenses': string;
        'organizations.status': string;
        'organizations.status.active': string;
        'organizations.status.pending': string;
        'organizations.status.suspended': string;
        'organizations.organizationName': string;
        'organizations.organizationNamePlaceHolder': string;
        'organizations.numberOfLicenses': string;
        'organizations.addressInformation': string;
        'organizations.streetAddress': string;
        'organizations.streetAddressPlaceholder': string;
        'organizations.cityPlaceholder': string;
        'organizations.state': string;
        'organizations.statePlaceholder': string;
        'organizations.zipcode': string;
        'organizations.zipcodePlaceholder': string;
        'organizations.country': string;
        'organizations.countryPlaceholder': string;
        'organizations.description': string;
        'organizations.descriptionPlaceholder': string;
        'organizations.creating': string;
        'organizations.general': string;
        'organizations.2fa': string;
        'organizations.delete': string;
        'organizations.deleteMessage': string;
        'organizations.deleteConfirm': string;
        'license.title': string;
        'license.subtitle': string;
        'license.appsSelected': string;
        'license.totalLicenses': string;
        'license.numOfLicenses': string;
        'license.selectedApps': string;
        'userMgmt.title': string;
        'userMgmt.addUser': string;
        'userMgmt.percentLicensesUsed': string;
        'userMgmt.exportUserList': string;
        'userMgmt.bulkUserOperations': string;
        'userMgmt.userAccessReport': string;
        '2fa.multiAuth': string;
        '2fa.enable2FA': string;
        '2fa.authenticationMethods': string;
        'billing.billingActions': string;
        'billing.generateInvoice': string;
        'billing.paymentHistory': string;
        'billing.updateBillingInfo': string;
        'personnel.title': string;
        'personnel.subtitle': string;
        'personnel.search': string;
        'personnel.error': string;
        'personnel.retry': string;
        'personnel.addPersonnel': string;
        'personnel.noPermission': string;
        'personnel.allOrganizations': string;
        'personnel.firstName': string;
        'personnel.lastName': string;
        'personnel.email': string;
        'personnel.phoneNumber': string;
        'personnel.organization': string;
        'personnel.roleSingle': string;
        'personnel.created': string;
        'personnel.updated': string;
        'personnel.edit': string;
        'personnel.noPersonnelFound': string;
        'personnel.autoSelectedFor': string;
        'personnel.selected': string;
        'personnel.oneRoleAllowed': string;
        'personnel.applications': string;
        'personnel.noApplicationsAvailable': string;
        'personnel.selectPermissions': string;
        'personnel.saving': string;
        'personnel.adding': string;
        'personnel.roleSelectOne': string;
        'personnel.roleSelectMultiple': string;
        'personnel.addNewPersonnel': string;
        'personnel.editPersonnel': string;
        'personnel.delete': string;
        'personnel.deleteMessage': string;
        'personnel.deleteConfirm': string;
        'personnel.changePassword': string;
        'personnel.passwordPlaceholder': string;
        'personnel.confirmPasswordPlaceholder': string;
        'roles.title': string;
        'roles.error': string;
        'roles.loading': string;
        'roles.create': string;
        'roles.edit': string;
        'roles.update': string;
        'roles.roleName': string;
        'roles.roleNamePlaceHolder': string;
        'roles.description': string;
        'roles.descriptionPlaceholder': string;
        'roles.permissions': string;
        'roles.personnel': string;
        'roles.organization': string;
        'roles.hierarchy': string;
        'roles.position': string;
        'roles.analytics': string;
        'roles.billing': string;
        'roles.schedule': string;
        'roles.shift': string;
        'roles.attendaceTracking': string;
        'roles.attendanceReports': string;
        'roles.report': string;
        'roles.alertRule': string;
        'roles.alertAndReportHistory': string;
        'roles.noRolesAvailable': string;
        'roles.delete': string;
        'roles.deleteMessage': string;
        'roles.deleteConfirm': string;
        'organization.title': string;
        'home.title': string;
        'home.welcome': string;
        'home.chooseApp': string;
        'home.searchPlaceholder': string;
        'home.noAppsFound': string;
        'home.open': string;
        'home.new': string;
        'home.recent': string;
        'home.list': string;
        'home.grid': string;
        'home.admin': string;
        'home.application': string;
    };
    es: {
        'header.title': string;
        'language.select': string;
        'language.current': string;
        'theme.select': string;
        'theme.light': string;
        'theme.dark': string;
        'theme.system': string;
        'common.save': string;
        'common.cancel': string;
        'common.apply': string;
        'common.close': string;
        'common.settings': string;
        'common.preferences': string;
        'nav.home': string;
        'nav.about': string;
        'nav.contact': string;
        'nav.help': string;
        'nav.dashboard': string;
        'nav.personnel': string;
        'nav.organizations': string;
        'nav.roles': string;
        'dashboard.title': string;
        'dashboard.subtitle': string;
        'dashboard.totalOrgs': string;
        'dashboard.activeUsers': string;
        'dashboard.licenseRevenue': string;
        'dashboard.growthRate': string;
        'dashboard.recentOrgs': string;
        'dashboard.licenseUsage': string;
        'dashboard.licensesSold': string;
        'dashboard.utilizationRate': string;
        'dashboard.noRecentOrgs': string;
        'dashboard.loading': string;
        'dashboard.error': string;
        'dashboard.noData': string;
        'organizations.title': string;
        'organizations.subtitle': string;
        'organizations.addOrganization': string;
        'organizations.addOrganizations': string;
        'organizations.search': string;
        'organizations.users': string;
        'organizations.month': string;
        'organizations.viewDetails': string;
        'organizations.manage': string;
        'organizations.details': string;
        'organizations.orgId': string;
        'organizations.domain': string;
        'organizations.domainPlaceholder': string;
        'organizations.createdDate': string;
        'organizations.contactEmail': string;
        'organizations.lastLogin': string;
        'organizations.usageStats': string;
        'organizations.monthlyRevenue': string;
        'organizations.annualRevenue': string;
        'organizations.licenseUtilization': string;
        'organizations.license': string;
        'organizations.licenses': string;
        'organizations.current': string;
        'organizations.currentBillingPeriod': string;
        'organizations.currentUsage': string;
        'organizations.billingInformation': string;
        'organizations.billingCycle': string;
        'organizations.nextBillingDate': string;
        'organizations.paymentMethod': string;
        'organizations.totalLicenses': string;
        'organizations.status': string;
        'organizations.status.active': string;
        'organizations.status.pending': string;
        'organizations.status.suspended': string;
        'organizations.organizationName': string;
        'organizations.organizationNamePlaceHolder': string;
        'organizations.numberOfLicenses': string;
        'organizations.addressInformation': string;
        'organizations.streetAddress': string;
        'organizations.streetAddressPlaceholder': string;
        'organizations.city': string;
        'organizations.cityPlaceholder': string;
        'organizations.state': string;
        'organizations.statePlaceholder': string;
        'organizations.zipcode': string;
        'organizations.zipcodePlaceholder': string;
        'organizations.country': string;
        'organizations.countryPlaceholder': string;
        'organizations.description': string;
        'organizations.descriptionPlaceholder': string;
        'organizations.creating': string;
        'organizations.general': string;
        'organizations.2fa': string;
        'organizations.delete': string;
        'organizations.deleteMessage': string;
        'organizations.deleteConfirm': string;
        'license.title': string;
        'license.subtitle': string;
        'license.appsSelected': string;
        'license.totalLicenses': string;
        'license.numOfLicenses': string;
        'license.selectedApps': string;
        'userMgmt.title': string;
        'userMgmt.addUser': string;
        'userMgmt.percentLicensesUsed': string;
        'userMgmt.exportUserList': string;
        'userMgmt.bulkUserOperations': string;
        'userMgmt.userAccessReport': string;
        'billing.billingActions': string;
        'billing.generateInvoice': string;
        'billing.paymentHistory': string;
        'billing.updateBillingInfo': string;
        '2fa.multiAuth': string;
        '2fa.enable2FA': string;
        '2fa.authenticationMethods': string;
        'personnel.title': string;
        'personnel.subtitle': string;
        'personnel.search': string;
        'personnel.error': string;
        'personnel.retry': string;
        'personnel.noPermission': string;
        'personnel.allOrganizations': string;
        'personnel.addPersonnel': string;
        'personnel.firstName': string;
        'personnel.lastName': string;
        'personnel.email': string;
        'personnel.phoneNumber': string;
        'personnel.organization': string;
        'personnel.roleSingle': string;
        'personnel.created': string;
        'personnel.updated': string;
        'personnel.edit': string;
        'personnel.noPersonnelFound': string;
        'personnel.autoSelectedFor': string;
        'personnel.selected': string;
        'personnel.oneRoleAllowed': string;
        'personnel.applications': string;
        'personnel.noApplicationsAvailable': string;
        'personnel.selectPermissions': string;
        'personnel.saving': string;
        'personnel.adding': string;
        'personnel.roleSelectOne': string;
        'personnel.roleSelectMultiple': string;
        'personnel.addNewPersonnel': string;
        'personnel.editPersonnel': string;
        'personnel.delete': string;
        'personnel.deleteMessage': string;
        'personnel.deleteConfirm': string;
        'personnel.changePassword': string;
        'personnel.passwordPlaceholder': string;
        'personnel.confirmPasswordPlaceholder': string;
        'roles.title': string;
        'roles.error': string;
        'roles.loading': string;
        'roles.create': string;
        'roles.edit': string;
        'roles.update': string;
        'roles.roleName': string;
        'roles.roleNamePlaceholder': string;
        'roles.description': string;
        'roles.descriptionPlaceholder': string;
        'roles.permissions': string;
        'roles.personnel': string;
        'roles.organization': string;
        'roles.hierarchy': string;
        'roles.position': string;
        'roles.analytics': string;
        'roles.billing': string;
        'roles.schedule': string;
        'roles.shift': string;
        'roles.attendaceTracking': string;
        'roles.attendanceReports': string;
        'roles.report': string;
        'roles.alertRule': string;
        'roles.alertAndReportHistory': string;
        'roles.noRolesAvailable': string;
        'roles.delete': string;
        'roles.deleteMessage': string;
        'roles.deleteConfirm': string;
        'organization.title': string;
        'home.title': string;
        'home.welcome': string;
        'home.chooseApp': string;
        'home.searchPlaceholder': string;
        'home.noAppsFound': string;
        'home.open': string;
        'home.new': string;
        'home.recent': string;
        'home.list': string;
        'home.grid': string;
        'home.admin': string;
        'home.application': string;
    };
};
declare const _default: {
    languages: Language[];
    createLanguageConfig: typeof createLanguageConfig;
    commonTranslations: {
        en: {
            'header.title': string;
            'language.select': string;
            'language.current': string;
            'theme.select': string;
            'theme.light': string;
            'theme.dark': string;
            'theme.system': string;
            'common.save': string;
            'common.cancel': string;
            'common.apply': string;
            'common.close': string;
            'common.settings': string;
            'common.preferences': string;
            'nav.home': string;
            'nav.about': string;
            'nav.contact': string;
            'nav.help': string;
            'nav.dashboard': string;
            'nav.personnel': string;
            'nav.organizations': string;
            'nav.roles': string;
            'dashboard.title': string;
            'dashboard.subtitle': string;
            'dashboard.totalOrgs': string;
            'dashboard.activeUsers': string;
            'dashboard.licenseRevenue': string;
            'dashboard.growthRate': string;
            'dashboard.recentOrgs': string;
            'dashboard.licenseUsage': string;
            'dashboard.licensesSold': string;
            'dashboard.utilizationRate': string;
            'dashboard.noRecentOrgs': string;
            'dashboard.loading': string;
            'dashboard.noData': string;
            'dashboard.error': string;
            'organizations.title': string;
            'organizations.subtitle': string;
            'organizations.addOrganization': string;
            'organizations.addOrganizations': string;
            'organizations.search': string;
            'organizations.users': string;
            'organizations.month': string;
            'organizations.viewDetails': string;
            'organizations.manage': string;
            'organizations.details': string;
            'organizations.orgId': string;
            'organizations.domain': string;
            'organizations.domainPlaceholder': string;
            'organizations.createdDate': string;
            'organizations.contactEmail': string;
            'organizations.lastLogin': string;
            'organizations.usageStats': string;
            'organizations.monthlyRevenue': string;
            'organizations.annualRevenue': string;
            'organizations.licenseUtilization': string;
            'organizations.license': string;
            'organizations.licenses': string;
            'organizations.current': string;
            'organizations.currentBillingPeriod': string;
            'organizations.currentUsage': string;
            'organizations.billingInformation': string;
            'organizations.billingCycle': string;
            'organizations.nextBillingDate': string;
            'organizations.paymentMethod': string;
            'organizations.totalLicenses': string;
            'organizations.status': string;
            'organizations.status.active': string;
            'organizations.status.pending': string;
            'organizations.status.suspended': string;
            'organizations.organizationName': string;
            'organizations.organizationNamePlaceHolder': string;
            'organizations.numberOfLicenses': string;
            'organizations.addressInformation': string;
            'organizations.streetAddress': string;
            'organizations.streetAddressPlaceholder': string;
            'organizations.cityPlaceholder': string;
            'organizations.state': string;
            'organizations.statePlaceholder': string;
            'organizations.zipcode': string;
            'organizations.zipcodePlaceholder': string;
            'organizations.country': string;
            'organizations.countryPlaceholder': string;
            'organizations.description': string;
            'organizations.descriptionPlaceholder': string;
            'organizations.creating': string;
            'organizations.general': string;
            'organizations.2fa': string;
            'organizations.delete': string;
            'organizations.deleteMessage': string;
            'organizations.deleteConfirm': string;
            'license.title': string;
            'license.subtitle': string;
            'license.appsSelected': string;
            'license.totalLicenses': string;
            'license.numOfLicenses': string;
            'license.selectedApps': string;
            'userMgmt.title': string;
            'userMgmt.addUser': string;
            'userMgmt.percentLicensesUsed': string;
            'userMgmt.exportUserList': string;
            'userMgmt.bulkUserOperations': string;
            'userMgmt.userAccessReport': string;
            '2fa.multiAuth': string;
            '2fa.enable2FA': string;
            '2fa.authenticationMethods': string;
            'billing.billingActions': string;
            'billing.generateInvoice': string;
            'billing.paymentHistory': string;
            'billing.updateBillingInfo': string;
            'personnel.title': string;
            'personnel.subtitle': string;
            'personnel.search': string;
            'personnel.error': string;
            'personnel.retry': string;
            'personnel.addPersonnel': string;
            'personnel.noPermission': string;
            'personnel.allOrganizations': string;
            'personnel.firstName': string;
            'personnel.lastName': string;
            'personnel.email': string;
            'personnel.phoneNumber': string;
            'personnel.organization': string;
            'personnel.roleSingle': string;
            'personnel.created': string;
            'personnel.updated': string;
            'personnel.edit': string;
            'personnel.noPersonnelFound': string;
            'personnel.autoSelectedFor': string;
            'personnel.selected': string;
            'personnel.oneRoleAllowed': string;
            'personnel.applications': string;
            'personnel.noApplicationsAvailable': string;
            'personnel.selectPermissions': string;
            'personnel.saving': string;
            'personnel.adding': string;
            'personnel.roleSelectOne': string;
            'personnel.roleSelectMultiple': string;
            'personnel.addNewPersonnel': string;
            'personnel.editPersonnel': string;
            'personnel.delete': string;
            'personnel.deleteMessage': string;
            'personnel.deleteConfirm': string;
            'personnel.changePassword': string;
            'personnel.passwordPlaceholder': string;
            'personnel.confirmPasswordPlaceholder': string;
            'roles.title': string;
            'roles.error': string;
            'roles.loading': string;
            'roles.create': string;
            'roles.edit': string;
            'roles.update': string;
            'roles.roleName': string;
            'roles.roleNamePlaceHolder': string;
            'roles.description': string;
            'roles.descriptionPlaceholder': string;
            'roles.permissions': string;
            'roles.personnel': string;
            'roles.organization': string;
            'roles.hierarchy': string;
            'roles.position': string;
            'roles.analytics': string;
            'roles.billing': string;
            'roles.schedule': string;
            'roles.shift': string;
            'roles.attendaceTracking': string;
            'roles.attendanceReports': string;
            'roles.report': string;
            'roles.alertRule': string;
            'roles.alertAndReportHistory': string;
            'roles.noRolesAvailable': string;
            'roles.delete': string;
            'roles.deleteMessage': string;
            'roles.deleteConfirm': string;
            'organization.title': string;
            'home.title': string;
            'home.welcome': string;
            'home.chooseApp': string;
            'home.searchPlaceholder': string;
            'home.noAppsFound': string;
            'home.open': string;
            'home.new': string;
            'home.recent': string;
            'home.list': string;
            'home.grid': string;
            'home.admin': string;
            'home.application': string;
        };
        es: {
            'header.title': string;
            'language.select': string;
            'language.current': string;
            'theme.select': string;
            'theme.light': string;
            'theme.dark': string;
            'theme.system': string;
            'common.save': string;
            'common.cancel': string;
            'common.apply': string;
            'common.close': string;
            'common.settings': string;
            'common.preferences': string;
            'nav.home': string;
            'nav.about': string;
            'nav.contact': string;
            'nav.help': string;
            'nav.dashboard': string;
            'nav.personnel': string;
            'nav.organizations': string;
            'nav.roles': string;
            'dashboard.title': string;
            'dashboard.subtitle': string;
            'dashboard.totalOrgs': string;
            'dashboard.activeUsers': string;
            'dashboard.licenseRevenue': string;
            'dashboard.growthRate': string;
            'dashboard.recentOrgs': string;
            'dashboard.licenseUsage': string;
            'dashboard.licensesSold': string;
            'dashboard.utilizationRate': string;
            'dashboard.noRecentOrgs': string;
            'dashboard.loading': string;
            'dashboard.error': string;
            'dashboard.noData': string;
            'organizations.title': string;
            'organizations.subtitle': string;
            'organizations.addOrganization': string;
            'organizations.addOrganizations': string;
            'organizations.search': string;
            'organizations.users': string;
            'organizations.month': string;
            'organizations.viewDetails': string;
            'organizations.manage': string;
            'organizations.details': string;
            'organizations.orgId': string;
            'organizations.domain': string;
            'organizations.domainPlaceholder': string;
            'organizations.createdDate': string;
            'organizations.contactEmail': string;
            'organizations.lastLogin': string;
            'organizations.usageStats': string;
            'organizations.monthlyRevenue': string;
            'organizations.annualRevenue': string;
            'organizations.licenseUtilization': string;
            'organizations.license': string;
            'organizations.licenses': string;
            'organizations.current': string;
            'organizations.currentBillingPeriod': string;
            'organizations.currentUsage': string;
            'organizations.billingInformation': string;
            'organizations.billingCycle': string;
            'organizations.nextBillingDate': string;
            'organizations.paymentMethod': string;
            'organizations.totalLicenses': string;
            'organizations.status': string;
            'organizations.status.active': string;
            'organizations.status.pending': string;
            'organizations.status.suspended': string;
            'organizations.organizationName': string;
            'organizations.organizationNamePlaceHolder': string;
            'organizations.numberOfLicenses': string;
            'organizations.addressInformation': string;
            'organizations.streetAddress': string;
            'organizations.streetAddressPlaceholder': string;
            'organizations.city': string;
            'organizations.cityPlaceholder': string;
            'organizations.state': string;
            'organizations.statePlaceholder': string;
            'organizations.zipcode': string;
            'organizations.zipcodePlaceholder': string;
            'organizations.country': string;
            'organizations.countryPlaceholder': string;
            'organizations.description': string;
            'organizations.descriptionPlaceholder': string;
            'organizations.creating': string;
            'organizations.general': string;
            'organizations.2fa': string;
            'organizations.delete': string;
            'organizations.deleteMessage': string;
            'organizations.deleteConfirm': string;
            'license.title': string;
            'license.subtitle': string;
            'license.appsSelected': string;
            'license.totalLicenses': string;
            'license.numOfLicenses': string;
            'license.selectedApps': string;
            'userMgmt.title': string;
            'userMgmt.addUser': string;
            'userMgmt.percentLicensesUsed': string;
            'userMgmt.exportUserList': string;
            'userMgmt.bulkUserOperations': string;
            'userMgmt.userAccessReport': string;
            'billing.billingActions': string;
            'billing.generateInvoice': string;
            'billing.paymentHistory': string;
            'billing.updateBillingInfo': string;
            '2fa.multiAuth': string;
            '2fa.enable2FA': string;
            '2fa.authenticationMethods': string;
            'personnel.title': string;
            'personnel.subtitle': string;
            'personnel.search': string;
            'personnel.error': string;
            'personnel.retry': string;
            'personnel.noPermission': string;
            'personnel.allOrganizations': string;
            'personnel.addPersonnel': string;
            'personnel.firstName': string;
            'personnel.lastName': string;
            'personnel.email': string;
            'personnel.phoneNumber': string;
            'personnel.organization': string;
            'personnel.roleSingle': string;
            'personnel.created': string;
            'personnel.updated': string;
            'personnel.edit': string;
            'personnel.noPersonnelFound': string;
            'personnel.autoSelectedFor': string;
            'personnel.selected': string;
            'personnel.oneRoleAllowed': string;
            'personnel.applications': string;
            'personnel.noApplicationsAvailable': string;
            'personnel.selectPermissions': string;
            'personnel.saving': string;
            'personnel.adding': string;
            'personnel.roleSelectOne': string;
            'personnel.roleSelectMultiple': string;
            'personnel.addNewPersonnel': string;
            'personnel.editPersonnel': string;
            'personnel.delete': string;
            'personnel.deleteMessage': string;
            'personnel.deleteConfirm': string;
            'personnel.changePassword': string;
            'personnel.passwordPlaceholder': string;
            'personnel.confirmPasswordPlaceholder': string;
            'roles.title': string;
            'roles.error': string;
            'roles.loading': string;
            'roles.create': string;
            'roles.edit': string;
            'roles.update': string;
            'roles.roleName': string;
            'roles.roleNamePlaceholder': string;
            'roles.description': string;
            'roles.descriptionPlaceholder': string;
            'roles.permissions': string;
            'roles.personnel': string;
            'roles.organization': string;
            'roles.hierarchy': string;
            'roles.position': string;
            'roles.analytics': string;
            'roles.billing': string;
            'roles.schedule': string;
            'roles.shift': string;
            'roles.attendaceTracking': string;
            'roles.attendanceReports': string;
            'roles.report': string;
            'roles.alertRule': string;
            'roles.alertAndReportHistory': string;
            'roles.noRolesAvailable': string;
            'roles.delete': string;
            'roles.deleteMessage': string;
            'roles.deleteConfirm': string;
            'organization.title': string;
            'home.title': string;
            'home.welcome': string;
            'home.chooseApp': string;
            'home.searchPlaceholder': string;
            'home.noAppsFound': string;
            'home.open': string;
            'home.new': string;
            'home.recent': string;
            'home.list': string;
            'home.grid': string;
            'home.admin': string;
            'home.application': string;
        };
    };
};
export default _default;
