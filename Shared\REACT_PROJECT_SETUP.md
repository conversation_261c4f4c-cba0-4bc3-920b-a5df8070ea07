# React Project Socket Integration Setup

This guide shows how to integrate the socket functionality from the Shared library into your React project.

## 🚀 Quick Setup

### 1. Install the Shared Library

```bash
# If using npm
npm install @your-org/shared

# If using yarn
yarn add @your-org/shared

# If using local development
npm link ../path/to/shared
```

### 2. Setup Socket Provider in Your Main App

```typescript
// src/App.tsx
import React from 'react';
import { SocketProvider } from '@your-org/shared';
import { useAuth } from './hooks/useAuth'; // Your existing auth hook

const App: React.FC = () => {
  // Get your authentication state
  const { isAuthenticated, accessToken, logout } = useAuth();
  
  // Socket server URL from environment
  const socketUrl = process.env.REACT_APP_SOCKET_URL || "ws://localhost:8081";

  return (
    <SocketProvider
      socketUrl={socketUrl}
      isAuthenticated={isAuthenticated}
      accessToken={accessToken}
      onAuthLogout={logout}
    >
      <Router>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/dashboard" element={<Dashboard />} />
          {/* Your other routes */}
        </Routes>
      </Router>
    </SocketProvider>
  );
};

export default App;
```

### 3. Use Socket Logout in Any Component

```typescript
// src/components/Header.tsx
import React from 'react';
import { useSocketLogout, useSocketConnection } from '@your-org/shared';

const Header: React.FC = () => {
  const logout = useSocketLogout();
  const { isConnected } = useSocketConnection();

  const handleLogout = async () => {
    try {
      await logout(); // Handles both socket and auth logout
      console.log('Successfully logged out');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header>
      <h1>My App</h1>
      <div>
        <span>Socket: {isConnected ? '🟢' : '🔴'}</span>
        <button onClick={handleLogout}>Logout</button>
      </div>
    </header>
  );
};

export default Header;
```

## 📋 Complete Integration Examples

### Example 1: Simple Component with Logout

```typescript
// src/components/LogoutButton.tsx
import React from 'react';
import { useSocketLogout } from '@your-org/shared';

const LogoutButton: React.FC = () => {
  const logout = useSocketLogout();

  const handleClick = async () => {
    const confirmed = window.confirm('Are you sure you want to logout?');
    if (confirmed) {
      try {
        await logout();
      } catch (error) {
        alert('Logout failed. Please try again.');
      }
    }
  };

  return <button onClick={handleClick}>Logout</button>;
};

export default LogoutButton;
```

### Example 2: Dashboard with Socket Events

```typescript
// src/components/Dashboard.tsx
import React, { useEffect, useState } from 'react';
import { useSocketContext } from '@your-org/shared';

const Dashboard: React.FC = () => {
  const { emit, on, off, isConnected, logout } = useSocketContext();
  const [notifications, setNotifications] = useState<string[]>([]);

  useEffect(() => {
    if (isConnected) {
      // Listen for notifications
      const handleNotification = (data: any) => {
        setNotifications(prev => [...prev, data.message]);
      };

      on('notification', handleNotification);

      // Cleanup
      return () => {
        off('notification', handleNotification);
      };
    }
  }, [isConnected, on, off]);

  const sendPing = () => {
    if (isConnected) {
      emit('ping', { timestamp: Date.now() });
    }
  };

  return (
    <div>
      <h2>Dashboard</h2>
      <p>Socket Status: {isConnected ? 'Connected' : 'Disconnected'}</p>
      
      <button onClick={sendPing} disabled={!isConnected}>
        Send Ping
      </button>
      
      <button onClick={logout}>
        Logout
      </button>

      <div>
        <h3>Notifications:</h3>
        <ul>
          {notifications.map((notif, index) => (
            <li key={index}>{notif}</li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default Dashboard;
```

### Example 3: Integration with Existing Auth Context

```typescript
// src/App.tsx - If you already have AuthContext
import React, { useContext } from 'react';
import { SocketProvider } from '@your-org/shared';
import { AuthContext } from './context/AuthContext';

const AppWithAuthContext: React.FC = () => {
  const { isAuthenticated, accessToken, logout } = useContext(AuthContext);
  const socketUrl = process.env.REACT_APP_SOCKET_URL || "ws://localhost:8081";

  return (
    <SocketProvider
      socketUrl={socketUrl}
      isAuthenticated={isAuthenticated}
      accessToken={accessToken}
      onAuthLogout={logout}
    >
      <YourAppComponents />
    </SocketProvider>
  );
};
```

## 🔧 Environment Configuration

### .env file setup

```bash
# .env
REACT_APP_SOCKET_URL=ws://your-socket-server.com:8081
# or for development
REACT_APP_SOCKET_URL=ws://localhost:8081
```

### TypeScript Configuration

If using TypeScript, make sure your `tsconfig.json` includes:

```json
{
  "compilerOptions": {
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  }
}
```

## 🎯 Available Hooks and Methods

### From `useSocketContext()`

```typescript
const {
  socket,           // Raw socket instance
  logout,           // Integrated logout function
  isConnected,      // Connection status
  connectionError,  // Error message if any
  emit,            // Send events to server
  on,              // Listen for events
  off              // Remove event listeners
} = useSocketContext();
```

### Individual Hooks

```typescript
// Get only logout function
const logout = useSocketLogout();

// Get only connection status
const { isConnected, connectionError } = useSocketConnection();

// Get only emit function
const emit = useSocketEmit();

// Get only event handlers
const { on, off } = useSocketEvents();
```

## 🚨 Error Handling

### Logout Error Handling

```typescript
const handleLogout = async () => {
  try {
    await logout();
    // Success - user is logged out
  } catch (error) {
    console.error('Logout failed:', error);
    
    // Show user-friendly error
    if (error.message.includes('Socket')) {
      alert('Network error during logout. You may need to refresh the page.');
    } else {
      alert('Logout failed. Please try again.');
    }
  }
};
```

### Connection Error Handling

```typescript
const { isConnected, connectionError } = useSocketConnection();

if (connectionError) {
  // Show connection error to user
  return (
    <div className="error">
      <p>Connection Error: {connectionError}</p>
      <button onClick={() => window.location.reload()}>
        Retry Connection
      </button>
    </div>
  );
}
```

## 📝 Best Practices

1. **Always wrap your app with SocketProvider** at the root level
2. **Use individual hooks** (`useSocketLogout`) for specific functionality
3. **Handle errors gracefully** with try-catch blocks
4. **Clean up event listeners** in useEffect cleanup functions
5. **Check connection status** before emitting events
6. **Use environment variables** for socket URLs

## 🔍 Troubleshooting

### Common Issues:

1. **"useSocketContext must be used within a SocketProvider"**
   - Make sure SocketProvider wraps your component tree

2. **Socket not connecting**
   - Check if `isAuthenticated` is true
   - Verify `accessToken` is provided
   - Check socket URL is correct

3. **Logout not working**
   - Ensure `onAuthLogout` callback is provided to SocketProvider
   - Check network connectivity
   - Verify server is responding to logout events

### Debug Mode:

The socket hooks include comprehensive logging. Open browser console to see:
- Connection status messages
- Event emissions and receptions
- Error messages
- Logout flow progress

## 📦 Package.json Dependencies

Make sure your project has these dependencies:

```json
{
  "dependencies": {
    "socket.io-client": "^4.x.x",
    "react": "^18.x.x",
    "@your-org/shared": "^1.x.x"
  }
}
```
