import { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios';
export declare const setApiUrl: (apiUrl: string) => void;
export declare const apiConfig: {
    currentUrl: string;
    apiUrl: string;
    environment: string;
};
export declare const apiClient: AxiosInstance;
export declare const api: {
    get: <T = any>(url: string, config?: AxiosRequestConfig) => Promise<AxiosResponse<T>>;
    post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => Promise<AxiosResponse<T>>;
    put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => Promise<AxiosResponse<T>>;
    patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => Promise<AxiosResponse<T>>;
    delete: <T = any>(url: string, config?: AxiosRequestConfig) => Promise<AxiosResponse<T>>;
};
export default apiClient;
