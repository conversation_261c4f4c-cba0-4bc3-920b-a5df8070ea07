import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';
const EditPersonnelModal = ({ open, onOpenChange, person, onUpdate, }) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        organization: '',
        role: '',
        isUser: false,
        status: 'Pending',
    });
    const organizations = ['TechCorp Solutions', 'Global Industries', 'Innovation Labs', 'StartupXYZ'];
    useEffect(() => {
        if (person) {
            setFormData({
                name: person.name,
                email: person.email,
                organization: person.organization,
                role: person.role,
                isUser: person.isUser,
                status: person.status,
            });
        }
    }, [person]);
    const handleSubmit = (e) => {
        e.preventDefault();
        if (!person)
            return;
        const updatedPerson = {
            ...person,
            name: formData.name,
            email: formData.email,
            organization: formData.organization,
            role: formData.role,
            isUser: formData.isUser,
            status: formData.status,
        };
        onUpdate(updatedPerson);
        onOpenChange(false);
    };
    if (!person)
        return null;
    return (_jsx(Dialog, { open: open, onOpenChange: onOpenChange, children: _jsxs(DialogContent, { className: "sm:max-w-[425px]", children: [_jsx(DialogHeader, { children: _jsx(DialogTitle, { children: "Edit Personnel" }) }), _jsxs("form", { onSubmit: handleSubmit, className: "space-y-4", children: [_jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "name", children: "Name" }), _jsx(Input, { id: "name", value: formData.name, onChange: (e) => setFormData({ ...formData, name: e.target.value }), placeholder: "Enter full name", required: true })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "email", children: "Email" }), _jsx(Input, { id: "email", type: "email", value: formData.email, onChange: (e) => setFormData({ ...formData, email: e.target.value }), placeholder: "Enter email address", required: true })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "organization", children: "Organization" }), _jsxs(Select, { value: formData.organization, onValueChange: (value) => setFormData({ ...formData, organization: value }), required: true, children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "Select organization" }) }), _jsx(SelectContent, { children: organizations.map((org) => (_jsx(SelectItem, { value: org, children: org }, org))) })] })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "role", children: "Role" }), _jsx(Input, { id: "role", value: formData.role, onChange: (e) => setFormData({ ...formData, role: e.target.value }), placeholder: "Enter job role", required: true })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "status", children: "Status" }), _jsxs(Select, { value: formData.status, onValueChange: (value) => setFormData({ ...formData, status: value }), children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, {}) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "Active", children: "Active" }), _jsx(SelectItem, { value: "Inactive", children: "Inactive" }), _jsx(SelectItem, { value: "Pending", children: "Pending" })] })] })] }), _jsxs("div", { className: "flex items-center justify-between", children: [_jsx(Label, { htmlFor: "isUser", children: "System User" }), _jsx(Switch, { id: "isUser", checked: formData.isUser, onCheckedChange: (checked) => setFormData({ ...formData, isUser: checked }) })] }), _jsxs("div", { className: "flex justify-end space-x-2 pt-4", children: [_jsx(Button, { type: "button", variant: "outline", onClick: () => onOpenChange(false), children: "Cancel" }), _jsx(Button, { type: "submit", children: "Update Personnel" })] })] })] }) }));
};
export default EditPersonnelModal;
