import React from "react";
interface PersonnelManagementInfoProps {
    appName: string;
    selectedOrganizationId: string;
    onOrganizationChange: (organizationId: string) => void;
    apiUrl?: string;
    orgApiUrl?: string;
    setPersonnel?: (personList: any) => void;
    hierarchy: any[];
}
declare const PersonnelManagementInfo: React.FC<PersonnelManagementInfoProps>;
export default PersonnelManagementInfo;
