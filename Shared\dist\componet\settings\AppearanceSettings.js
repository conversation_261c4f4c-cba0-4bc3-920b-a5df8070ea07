import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { NextThemeToggle } from '../theme/NextThemeToggle';
import { LanguageSelector } from '../language/LanguageSelector';
import { useLanguage } from '../../i18n/LanguageProvider';
export function AppearanceSettings({ showThemeSection = true, showLanguageSection = true, showCurrentStatus = true, className = '', title = 'Appearance Settings', description = 'Customize the look and feel of your application', useTheme, }) {
    const themeData = useTheme?.();
    const { currentLanguage, isRTL } = useLanguage();
    return (_jsxs("div", { className: `space-y-6 ${className}`, children: [_jsxs("div", { className: "space-y-2", children: [_jsx("h2", { className: "text-2xl font-bold tracking-tight", children: title }), description && (_jsx("p", { className: "text-muted-foreground", children: description }))] }), showCurrentStatus && (_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsx(CardTitle, { className: "text-lg", children: "Current Settings" }) }), _jsx(CardContent, { className: "space-y-4", children: _jsxs("div", { className: "flex flex-wrap gap-3", children: [showThemeSection && themeData && (_jsxs("div", { className: "flex items-center gap-2", children: [_jsx("span", { className: "text-sm font-medium", children: "Theme:" }), _jsxs(Badge, { variant: "secondary", children: [themeData.theme, " ", themeData.theme === 'system' && `(${themeData.resolvedTheme})`] })] })), showLanguageSection && (_jsxs("div", { className: "flex items-center gap-2", children: [_jsx("span", { className: "text-sm font-medium", children: "Language:" }), _jsxs(Badge, { variant: "secondary", className: "flex items-center gap-1", children: [currentLanguage?.flag && (_jsx("span", { children: currentLanguage.flag })), currentLanguage?.name] }), isRTL && (_jsx(Badge, { variant: "outline", className: "text-xs", children: "RTL" }))] }))] }) })] })), showThemeSection && useTheme && (_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { className: "text-lg", children: "Theme" }), _jsx("p", { className: "text-sm text-muted-foreground", children: "Choose how the application looks to you" })] }), _jsx(CardContent, { children: _jsx(NextThemeToggle, { variant: "cards", showDescriptions: true, className: "max-w-2xl", useTheme: useTheme }) })] })), showLanguageSection && (_jsxs(Card, { children: [_jsxs(CardHeader, { children: [_jsx(CardTitle, { className: "text-lg", children: "Language" }), _jsx("p", { className: "text-sm text-muted-foreground", children: "Select your preferred language for the interface" })] }), _jsx(CardContent, { children: _jsx(LanguageSelector, { variant: "cards", showNativeNames: true, showCodes: true, className: "max-w-4xl" }) })] }))] }));
}
// Compact settings for navbar/header
export function CompactAppearanceSettings() {
    return (_jsx("div", { className: "flex items-center gap-3", children: _jsx(LanguageSelector, { variant: "icon" }) }));
}
// Settings dropdown for limited space
export function AppearanceDropdown() {
    return (_jsxs(Card, { className: "w-80", children: [_jsx(CardHeader, { className: "pb-3", children: _jsx(CardTitle, { className: "text-base", children: "Appearance" }) }), _jsxs(CardContent, { className: "space-y-4", children: [_jsx("div", { className: "border-t my-4" }), _jsxs("div", { children: [_jsx("label", { className: "text-sm font-medium mb-2 block", children: "Language" }), _jsx(LanguageSelector, { variant: "select" })] })] })] }));
}
// Quick toggle buttons for toolbars
export function AppearanceToolbar() {
    return (_jsxs("div", { className: "flex items-center gap-1 p-1 bg-muted rounded-lg", children: [_jsx("div", { className: "w-px h-6 bg-border mx-1" }), _jsx(LanguageSelector, { variant: "buttons", size: "sm", maxDisplayLanguages: 3, showNativeNames: false })] }));
}
export default AppearanceSettings;
