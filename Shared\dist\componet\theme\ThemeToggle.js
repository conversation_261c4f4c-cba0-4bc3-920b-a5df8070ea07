import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Moon, Sun, Monitor, Check } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { useUniversalTheme } from '../../theme/UniversalThemeProvider';
const themeOptions = [
    {
        value: 'light',
        label: 'Light',
        icon: _jsx(Sun, { className: "h-4 w-4" }),
        description: 'Light theme for bright environments'
    },
    {
        value: 'dark',
        label: 'Dark',
        icon: _jsx(Moon, { className: "h-4 w-4" }),
        description: 'Dark theme for low-light environments'
    },
    {
        value: 'system',
        label: 'System',
        icon: _jsx(Monitor, { className: "h-4 w-4" }),
        description: 'Follow your system preferences'
    }
];
export function ThemeToggle({ variant = 'select', size = 'default', showLabels = true, showDescriptions = false, className = '', }) {
    const { theme, setTheme, resolvedTheme } = useUniversalTheme();
    const currentTheme = themeOptions.find(option => option.value === theme);
    const sizeClasses = {
        sm: 'h-8 text-sm',
        default: 'h-9 text-sm',
        lg: 'h-10 text-base',
    };
    // Icon-only toggle (cycles through themes)
    if (variant === 'icon') {
        const cycleTheme = () => {
            const themes = ['light', 'dark', 'system'];
            const currentIndex = themes.indexOf(theme);
            const nextIndex = (currentIndex + 1) % themes.length;
            setTheme(themes[nextIndex]);
        };
        return (_jsx(Button, { variant: "outline", size: size, onClick: cycleTheme, className: `${sizeClasses[size]} ${className}`, title: `Current: ${currentTheme?.label} - Click to cycle`, children: currentTheme?.icon }));
    }
    // Button group variant
    if (variant === 'buttons') {
        return (_jsx("div", { className: `flex gap-1 p-1 bg-muted rounded-lg ${className}`, children: themeOptions.map((option) => {
                const isSelected = option.value === theme;
                return (_jsx(Button, { variant: isSelected ? 'default' : 'ghost', size: size, onClick: () => setTheme(option.value), className: `${sizeClasses[size]} ${isSelected ? 'shadow-sm' : ''}`, title: option.description, children: _jsxs("div", { className: "flex items-center gap-2", children: [option.icon, showLabels && _jsx("span", { children: option.label })] }) }, option.value));
            }) }));
    }
    // Card variant for settings pages
    if (variant === 'cards') {
        return (_jsx("div", { className: `grid grid-cols-1 md:grid-cols-3 gap-3 ${className}`, children: themeOptions.map((option) => {
                const isSelected = option.value === theme;
                return (_jsx(Card, { className: `cursor-pointer transition-all duration-200 ${isSelected
                        ? 'ring-2 ring-primary border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'}`, onClick: () => setTheme(option.value), children: _jsxs(CardContent, { className: "p-4", children: [_jsxs("div", { className: "flex items-center justify-between mb-2", children: [_jsxs("div", { className: "flex items-center gap-2", children: [_jsx("div", { className: "text-muted-foreground", children: option.icon }), _jsx("span", { className: "font-medium", children: option.label })] }), isSelected && (_jsx("div", { className: "w-4 h-4 bg-primary rounded-full flex items-center justify-center", children: _jsx(Check, { className: "w-2.5 h-2.5 text-primary-foreground" }) }))] }), showDescriptions && (_jsx("p", { className: "text-xs text-muted-foreground", children: option.description })), option.value === 'system' && (_jsxs(Badge, { variant: "outline", className: "text-xs mt-2", children: ["Currently: ", resolvedTheme] }))] }) }, option.value));
            }) }));
    }
    // Default select variant
    return (_jsxs(Select, { value: theme, onValueChange: setTheme, children: [_jsx(SelectTrigger, { className: `w-40 ${sizeClasses[size]} bg-background border border-input hover:bg-accent hover:text-accent-foreground transition-colors ${className}`, children: _jsxs("div", { className: "flex items-center gap-2", children: [currentTheme?.icon, _jsx(SelectValue, { placeholder: "Select theme" })] }) }), _jsx(SelectContent, { align: "end", className: "w-48", children: themeOptions.map((option) => (_jsx(SelectItem, { value: option.value, className: "cursor-pointer", children: _jsxs("div", { className: "flex items-center gap-3 py-1", children: [_jsx("div", { className: "text-muted-foreground", children: option.icon }), _jsxs("div", { className: "flex flex-col", children: [_jsx("span", { className: "font-medium", children: option.label }), showDescriptions && (_jsx("span", { className: "text-xs text-muted-foreground", children: option.description })), option.value === 'system' && (_jsxs("span", { className: "text-xs text-muted-foreground", children: ["Currently: ", resolvedTheme] }))] })] }) }, option.value))) })] }));
}
// Simplified theme toggle button (just icon)
export function ThemeToggleButton() {
    return _jsx(ThemeToggle, { variant: "icon" });
}
// Theme selector for settings pages
export function ThemeSelector() {
    return (_jsx(ThemeToggle, { variant: "cards", showDescriptions: true, className: "max-w-2xl" }));
}
// Compact theme switcher for navbars
export function ThemeSwitcher() {
    return (_jsx(ThemeToggle, { variant: "buttons", size: "sm", showLabels: false }));
}
export default ThemeToggle;
