import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, } from "../ui/alert-dialog";
import { buttonVariants, cn } from "../ui";
const ConfirmDialog = ({ open, onCancel, onConfirm, title, description, confirmText = "Confirm", cancelText = "Cancel", }) => {
    return (_jsx(AlertDialog, { open: open, onOpenChange: (isOpen) => !isOpen && onCancel(), children: _jsxs(AlertDialogContent, { children: [_jsxs(AlertDialogHeader, { children: [_jsx(AlertDialogTitle, { children: title }), _jsx(AlertDialogDescription, { children: description })] }), _jsxs(AlertDialogFooter, { children: [_jsx(AlertDialogCancel, { onClick: onCancel, children: cancelText }), _jsx(AlertDialogAction, { className: cn(buttonVariants({ variant: "destructive" })), onClick: onConfirm, children: confirmText })] })] }) }));
};
export default ConfirmDialog;
