import { useQuery } from "@tanstack/react-query";

// Master API types
export interface Permission {
  _id: string;
  resource: string;
  action: string;
  description: string;
  createdAt: string;
  showGlobal: boolean;
}

// Master API functions
export const masterApi = {
  getPermissions: async (
    adminApiUrl: string,
    showGlobal?: boolean
  ): Promise<Permission[]> => {
    try {
      // Use the provided adminApiUrl to call the permissions endpoint
      const endpoint = `/api/roles/permission/${showGlobal}`;

      const fullUrl = `${adminApiUrl.replace(/\/$/, "")}${endpoint}`;

      // Create a new axios instance or use fetch to call the specific URL
      const response = await fetch(fullUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token") || ""}`,
        },
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error("You do not have permission to access this resource");
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const json = await response.json();

      if (!json.success || !Array.isArray(json.data)) {
        throw new Error("Invalid response format");
      }

      return json.data;
    } catch (error: any) {
      if (
        error.message.includes("403") ||
        error.message.includes("permission")
      ) {
        throw new Error("You do not have permission to access this resource");
      }
      throw new Error("Failed to fetch permissions");
    }
  },

  getOrgRolesByOrganization: async (
    apiUrl: string,
    organizationId?: string
  ): Promise<any> => {
    try {
      // Use the provided apiUrl to call the organization roles endpoint
      const endpoint = "/api/roles";

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (organizationId) {
        queryParams.append("organizationId", organizationId);
      }

      const queryString = queryParams.toString();
      const fullUrl = `${apiUrl.replace(/\/$/, "")}${endpoint}${
        queryString ? `?${queryString}` : ""
      }`;

      const response = await fetch(fullUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token") || ""}`,
        },
      });

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error("You do not have permission to access this resource");
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const json = await response.json();

      if (!json.success || !Array.isArray(json.data)) {
        throw new Error("Invalid response format");
      }

      // Normalize the roles data
      const normalizedData = json.data.map((role: any) => ({
        ...role,
        id: role.id ?? role._id, // Normalize id
      }));

      return normalizedData;
    } catch (error: any) {
      console.error(
        `❌ [Master] Failed to fetch organization roles from ${apiUrl}:`,
        error
      );
      if (
        error.message.includes("403") ||
        error.message.includes("permission")
      ) {
        throw new Error("You do not have permission to access this resource");
      }
      throw new Error("Failed to fetch organization roles");
    }
  },
};

// Query keys for master operations
export const masterKeys = {
  all: ["master"] as const,
  permissions: (adminApiUrl: string) =>
    [...masterKeys.all, "permissions", adminApiUrl] as const,
  orgRoles: (apiUrl: string, organizationId?: string) =>
    [...masterKeys.all, "orgRoles", apiUrl, organizationId || "all"] as const,
};

// Master hooks
export const usePermissions = (adminApiUrl: string) => {
  return useQuery<Permission[]>({
    queryKey: masterKeys.permissions(adminApiUrl),
    queryFn: () => masterApi.getPermissions(adminApiUrl, true),
    staleTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!adminApiUrl, // Only run query if adminApiUrl is provided
  });
};

export const useOrgPermissions = (adminApiUrl: string) => {
  return useQuery<Permission[]>({
    queryKey: masterKeys.permissions(adminApiUrl),
    queryFn: () => masterApi.getPermissions(adminApiUrl, false),
    staleTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!adminApiUrl, // Only run query if adminApiUrl is provided
  });
};

export const useOrgRolesByOrganization = (
  organizationId?: string,
  apiUrl?: string
) => {
  return useQuery<any>({
    queryKey: masterKeys.orgRoles(apiUrl || "", organizationId),
    queryFn: () =>
      masterApi.getOrgRolesByOrganization(apiUrl || "", organizationId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!apiUrl, // Only run query if apiUrl is provided
  });
};
