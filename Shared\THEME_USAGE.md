# Universal Theme & Language System Usage Guide

This guide shows how to use the CommonComponent theme and language system in different React projects.

## Installation

```bash
npm install @relativity/common-components
```

## Quick Start

### 1. Basic React App Setup (Theme + Language)

```tsx
// App.tsx
import React from 'react';
import {
  UniversalThemeProvider,
  LanguageProvider,
  ThemeToggle,
  LanguageSelector,
  reactThemeConfig,
  reactLanguageConfig,
  commonTranslations
} from '@relativity/common-components';

function App() {
  return (
    <ThemeProvider {...reactThemeConfig}>
      <LanguageProvider
        {...reactLanguageConfig}
        translations={commonTranslations}
      >
        <div className="min-h-screen bg-background text-foreground">
          <header className="p-4 border-b">
            <div className="flex justify-between items-center">
              <h1>My App</h1>
              <div className="flex items-center gap-3">
                <LanguageSelector variant="select" />
                <ThemeToggle variant="select" />
              </div>
            </div>
          </header>
          <main className="p-4">
            {/* Your app content */}
          </main>
        </div>
      </LanguageProvider>
    </ThemeProvider>
  );
}

export default App;
```

### 2. Next.js Setup

```tsx
// pages/_app.tsx or app/layout.tsx
import { 
  UniversalThemeProvider, 
  nextThemeConfig,
  injectThemeVariables 
} from '@relativity/common-components';

// Inject CSS variables (call once)
if (typeof window !== 'undefined') {
  injectThemeVariables();
}

export default function App({ Component, pageProps }) {
  return (
    <UniversalThemeProvider {...nextThemeConfig}>
      <Component {...pageProps} />
    </UniversalThemeProvider>
  );
}
```

### 3. Vite/Create React App Setup

```tsx
// main.tsx or index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { 
  UniversalThemeProvider, 
  viteThemeConfig,
  injectThemeVariables 
} from '@relativity/common-components';
import App from './App';

// Inject CSS variables
injectThemeVariables();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <UniversalThemeProvider {...viteThemeConfig}>
      <App />
    </UniversalThemeProvider>
  </React.StrictMode>
);
```

## Language System

### LanguageSelector Component

The `LanguageSelector` component provides a flexible language selection interface with multiple variants and customization options.

#### Props

```tsx
interface LanguageSelectorProps {
  variant?: 'select' | 'buttons' | 'cards' | 'icon';
  size?: 'sm' | 'default' | 'lg';
  showNativeNames?: boolean;
  showFlags?: boolean;
  showCodes?: boolean;
  className?: string;
  maxDisplayLanguages?: number;
}
```

#### Variants

```tsx
import {
  LanguageSelector,
  LanguageToggleButton,
  LanguageSettings,
  LanguageSwitcher,
  LanguageDropdown
} from '@relativity/shared';

// Select dropdown (default) - Compact dropdown with language options
<LanguageSelector variant="select" />
<LanguageSelector variant="select" showNativeNames={true} showFlags={true} />

// Button group - Horizontal buttons for each language
<LanguageSelector variant="buttons" showNativeNames={true} />
<LanguageSelector variant="buttons" size="sm" maxDisplayLanguages={3} />

// Card layout - Grid of cards, ideal for settings pages
<LanguageSelector variant="cards" showCodes={true} />
<LanguageSelector variant="cards" showNativeNames={true} showCodes={true} />

// Icon only - Single button that cycles through languages
<LanguageSelector variant="icon" />
<LanguageSelector variant="icon" size="lg" />
```

#### Size Options

```tsx
// Small size (height: 32px)
<LanguageSelector size="sm" />

// Default size (height: 36px)
<LanguageSelector size="default" />

// Large size (height: 40px)
<LanguageSelector size="lg" />
```

#### Display Options

```tsx
// Show native language names (e.g., "Español" instead of "Spanish")
<LanguageSelector showNativeNames={true} />

// Show country flags
<LanguageSelector showFlags={true} />

// Show language codes (e.g., "en", "es")
<LanguageSelector showCodes={true} />

// Limit number of displayed languages
<LanguageSelector maxDisplayLanguages={5} />

// Custom styling
<LanguageSelector className="custom-class" />
```

#### Pre-configured Components

```tsx
// Icon-only toggle button (cycles through first 3 languages)
<LanguageToggleButton />

// Settings page layout with cards, native names, and codes
<LanguageSettings />

// Compact button group for navbars (first 3 languages, no native names)
<LanguageSwitcher />

// Full dropdown with native names and flags
<LanguageDropdown />
```

#### Variant Behaviors

**Select Variant (`variant="select"`)**
- Displays as a dropdown menu with 128px fixed width
- Shows current language in trigger button
- Dropdown items show language name, native name (if different), codes (if enabled), and RTL indicator
- Best for: Headers, toolbars, compact spaces

**Buttons Variant (`variant="buttons"`)**
- Displays languages as horizontal button group
- Selected language has `default` variant styling, others use `ghost`
- Wrapped in muted background container with rounded corners
- Best for: Settings sections, when you want all options visible

**Cards Variant (`variant="cards"`)**
- Grid layout (1 column on mobile, 2 on tablet, 3 on desktop)
- Each language as a clickable card with hover effects
- Selected card has primary ring and background tint
- Shows check icon for selected language
- Displays RTL badge for right-to-left languages
- Best for: Settings pages, detailed language selection

**Icon Variant (`variant="icon"`)**
- Single button that cycles through first 3 languages
- Shows current language flag or globe icon
- Click to cycle to next language
- Tooltip shows current language and cycling behavior
- Best for: Minimal UI, quick language switching

#### Advanced Usage

```tsx
// Custom configuration for specific use cases
<LanguageSelector
  variant="buttons"
  size="sm"
  showNativeNames={false}
  showFlags={true}
  showCodes={false}
  maxDisplayLanguages={4}
  className="bg-muted rounded-lg p-1"
/>

// Settings page with full options
<LanguageSelector
  variant="cards"
  showNativeNames={true}
  showFlags={true}
  showCodes={true}
  className="max-w-4xl grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
/>

// Responsive language selector
<LanguageSelector
  variant="icon"
  className="md:hidden"  // Show icon on mobile
/>
<LanguageSelector
  variant="buttons"
  maxDisplayLanguages={3}
  className="hidden md:flex"  // Show buttons on desktop
/>
```

#### Accessibility Features

- **Keyboard Navigation**: All variants support full keyboard navigation
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Tooltips**: Icon variant includes descriptive tooltips
- **Focus Management**: Clear focus indicators and logical tab order
- **RTL Support**: Automatic detection and display of right-to-left languages

#### RTL Language Support

The LanguageSelector automatically handles right-to-left (RTL) languages:

```tsx
// RTL languages are automatically detected and marked
const languages = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦', rtl: true },
  { code: 'he', name: 'Hebrew', nativeName: 'עברית', flag: '🇮🇱', rtl: true },
];

// RTL indicator is shown in select and cards variants
<LanguageSelector variant="select" />  // Shows "Right-to-left" text
<LanguageSelector variant="cards" />   // Shows "RTL" badge
```

### Using the Language Hook

```tsx
import { useLanguage } from '@relativity/shared';

function MyComponent() {
  const {
    language,
    setLanguage,
    currentLanguage,
    isRTL,
    t
  } = useLanguage();

  return (
    <div dir={isRTL ? 'rtl' : 'ltr'}>
      <p>{t('common.welcome')}</p>
      <p>Current: {currentLanguage?.name}</p>
      <p>RTL Mode: {isRTL ? 'Yes' : 'No'}</p>

      <button onClick={() => setLanguage('es')}>
        Switch to Spanish
      </button>
    </div>
  );
}
```

## Theme Components

### ThemeToggle Variants

```tsx
import { 
  ThemeToggle, 
  ThemeToggleButton, 
  ThemeSelector, 
  ThemeSwitcher 
} from '@relativity/common-components';

// Select dropdown (default)
<ThemeToggle variant="select" />

// Button group
<ThemeToggle variant="buttons" showLabels={true} />

// Card layout for settings
<ThemeToggle variant="cards" showDescriptions={true} />

// Icon only (cycles themes)
<ThemeToggle variant="icon" />

// Pre-configured components
<ThemeToggleButton />  // Icon only
<ThemeSelector />      // Cards with descriptions
<ThemeSwitcher />      // Compact buttons
```

### Using the Theme Hook

```tsx
import { useUniversalTheme, useThemeAware } from '@relativity/common-components';

function MyComponent() {
  // Basic theme access
  const { theme, setTheme, resolvedTheme } = useUniversalTheme();
  
  // Theme-aware utilities
  const { 
    isDark, 
    isLight, 
    isSystem, 
    toggleTheme, 
    cycleTheme 
  } = useThemeAware();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Resolved theme: {resolvedTheme}</p>
      <p>Is dark mode: {isDark}</p>
      
      <button onClick={toggleTheme}>
        Toggle Theme
      </button>
      
      <button onClick={cycleTheme}>
        Cycle Theme
      </button>
    </div>
  );
}
```

## Framework-Specific Configurations

### Tailwind CSS

```tsx
// Use with Tailwind's dark mode
import { UniversalThemeProvider, tailwindThemeConfig } from '@relativity/common-components';

<UniversalThemeProvider {...tailwindThemeConfig}>
  <div className="bg-white dark:bg-gray-900 text-black dark:text-white">
    {/* Your content */}
  </div>
</UniversalThemeProvider>
```

### Material-UI

```tsx
import { UniversalThemeProvider, muiThemeConfig } from '@relativity/common-components';

<UniversalThemeProvider {...muiThemeConfig}>
  {/* MUI components will read data-theme attribute */}
</UniversalThemeProvider>
```

### Chakra UI

```tsx
import { UniversalThemeProvider, chakraThemeConfig } from '@relativity/common-components';

<UniversalThemeProvider {...chakraThemeConfig}>
  {/* Chakra components will read data-theme attribute */}
</UniversalThemeProvider>
```

## Combined Settings Components

```tsx
import {
  AppearanceSettings,
  CompactAppearanceSettings,
  AppearanceDropdown,
  AppearanceToolbar
} from '@relativity/common-components';

// Full settings page
<AppearanceSettings />

// Compact for navbar
<CompactAppearanceSettings />

// Dropdown for limited space
<AppearanceDropdown />

// Toolbar with buttons
<AppearanceToolbar />
```

## Language Configuration

```tsx
import {
  createLanguageConfig,
  LanguageProvider,
  europeanLanguages,
  globalLanguages,
  commonTranslations
} from '@relativity/common-components';

const customLanguageConfig = createLanguageConfig({
  defaultLanguage: 'en',
  languages: europeanLanguages,
  detectBrowserLanguage: true,
});

// With custom translations
const myTranslations = {
  en: { 'app.title': 'My App' },
  es: { 'app.title': 'Mi Aplicación' },
  fr: { 'app.title': 'Mon App' },
};

<LanguageProvider
  {...customLanguageConfig}
  translations={myTranslations}
>
  {/* Your app */}
</LanguageProvider>
```

## Custom Configuration

```tsx
import { createThemeConfig, UniversalThemeProvider } from '@relativity/common-components';

const customConfig = createThemeConfig({
  defaultTheme: 'dark',
  storageKey: 'my-app-theme',
  themes: ['light', 'dark', 'blue', 'green'],
  attribute: 'data-theme',
  value: {
    light: 'light-mode',
    dark: 'dark-mode',
    blue: 'blue-theme',
    green: 'green-theme',
  },
});

<UniversalThemeProvider {...customConfig}>
  {/* Your app */}
</UniversalThemeProvider>
```

## CSS Variables

The theme system provides CSS variables that work with any CSS framework:

```css
/* These variables are automatically injected */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  /* ... more variables */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  /* ... more variables */
}

/* Use in your CSS */
.my-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-color: hsl(var(--border));
}
```

## Utilities

```tsx
import { themeUtils, applyTheme } from '@relativity/common-components';

// Check user preferences
const prefersDark = themeUtils.prefersDark();
const prefersReducedMotion = themeUtils.prefersReducedMotion();

// Manual theme application
applyTheme('dark', { storageKey: 'my-theme' });

// Storage utilities
const storedTheme = themeUtils.getStoredTheme('my-theme');
themeUtils.setStoredTheme('dark', 'my-theme');
themeUtils.removeStoredTheme('my-theme');
```

## TypeScript Support

All components and hooks are fully typed:

```tsx
import type { 
  Theme, 
  ThemeConfig, 
  ThemeProviderState 
} from '@relativity/common-components';

const theme: Theme = 'dark';
const config: ThemeConfig = { defaultTheme: 'system' };
```

## Best Practices

1. **SSR/SSG**: Use `disableTransitionOnChange: true` for Next.js to prevent flash
2. **Performance**: The theme provider uses minimal re-renders
3. **Accessibility**: System theme detection respects user preferences
4. **Storage**: Gracefully handles localStorage errors
5. **Flexibility**: Works with any CSS framework or custom styles

## Migration from next-themes

If you're migrating from `next-themes`, the API is very similar:

```tsx
// Before (next-themes)
import { ThemeProvider, useTheme } from 'next-themes';

// After (CommonComponent)
import { UniversalThemeProvider, useUniversalTheme } from '@relativity/common-components';
```

The main differences:
- `UniversalThemeProvider` instead of `ThemeProvider`
- `useUniversalTheme` instead of `useTheme`
- Additional utilities like `useThemeAware`
- More configuration options
- Framework-agnostic design
