import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "../lib/api";
import { useToast } from "../component/ui/use-toast";
import { Permission } from "../lib/permissionUtils";

// Define Role types
export interface Role {
  _id?: string;
  name: string;
  description: string;
  permissionsList: Permission[];
}

export interface CreateRoleData {
  name: string;
  description: string;
  permissionsList: Permission[];
}

export interface UpdateRoleData extends CreateRoleData {
  id: string;
}

// Enhanced types to match backend response

export const rolesApi = {
  // Enhanced getRoles to support backend parameters
  getRoles: async (appName: string): Promise<any> => {
    try {
      console.log("getRoles called with appName:", appName);
      // Always use the same endpoint for both applications - no conditional logic needed
      const endpoint = "/api/roles";

      const response = await api.get(endpoint);
      const json = response.data;

      if (!json.success || !Array.isArray(json.data)) {
        throw new Error("Invalid response format");
      }

      // Normalize the roles data
      const normalizedData = json.data.map((role: any) => ({
        ...role,
        id: role.id ?? role._id, // Normalize id
      }));

      return {
        ...json,
        data: normalizedData,
      };
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to access this resource");
      }
      throw new Error("Failed to fetch roles");
    }
  },

  // Keep existing methods unchanged for backward compatibility
  getRolesSimple: async (appName: string): Promise<any> => {
    const response = await rolesApi.getRoles(appName);
    if (!response || !Array.isArray(response.data)) {
      throw new Error("You do not have permission to access this resource");
    }

    return { data: response.data };
  },

  getRole: async (id: string): Promise<Role | null> => {
    try {
      // Always use adminApiUrl - same endpoint for both applications
      const endpoint = `/api/roles/${id}`;

      const response = await api.get(endpoint);
      const result = response.data.data || response.data;
      return result;
    } catch (error: any) {
      console.error(`❌ Failed to fetch role ${id}:`, error);
      if (error.response?.status === 404) return null;
      throw new Error("Failed to fetch role");
    }
  },

  createRole: async (data: CreateRoleData): Promise<Role> => {
    try {
      // Always use adminApiUrl - same endpoint for both applications
      const endpoint = "/api/roles";
      const response = await api.post(endpoint, data);
      const result = response.data.data || response.data;

      return result;
    } catch (error: any) {
      console.error(`❌ Failed to create role:`, error);
      throw new Error("Failed to create role");
    }
  },

  updateRole: async (data: UpdateRoleData): Promise<Role> => {
    try {
      const { id, ...updateData } = data;
      // Always use adminApiUrl - same endpoint for both applications
      const endpoint = `/api/roles/${id}`;

      const response = await api.put(endpoint, updateData);
      const result = response.data.data || response.data;

      return result;
    } catch (error: any) {
      console.error(`❌ Failed to update role ${data.id}:`, error);
      throw new Error("Failed to update role");
    }
  },

  deleteRole: async (id: string): Promise<void> => {
    try {
      // Always use adminApiUrl - same endpoint for both applications
      const endpoint = `/api/roles/${id}`;

      await api.delete(endpoint);
    } catch (error: any) {
      throw new Error("Failed to delete role");
    }
  },
};

export const roleKeys = {
  all: ["roles"] as const,
  lists: () => [...roleKeys.all, "list"] as const,
  list: () => [...roleKeys.lists()] as const,
  details: () => [...roleKeys.all, "detail"] as const,
  detail: (id: string) => [...roleKeys.details(), id] as const,
};

export const permissionKeys = {
  all: ["permissions"] as const,
  lists: () => [...permissionKeys.all, "list"] as const,
  list: () => [...permissionKeys.lists()] as const,
};

// Enhanced hook that supports full backend functionality
export const useRolesWithPagination = (appName: string) => {
  return useQuery({
    queryKey: [...roleKeys.list(), appName],
    queryFn: () => rolesApi.getRoles(appName),
    staleTime: 5 * 60 * 1000,
  });
};

// Keep original hook for backward compatibility - unchanged functionality
export const useRoles = (appName: string) => {
  return useQuery({
    queryKey: [...roleKeys.lists(), appName],
    queryFn: () => rolesApi.getRolesSimple(appName),
    staleTime: 5 * 60 * 1000,
  });
};

export const useRole = (id: string) => {
  return useQuery<Role | null>({
    queryKey: [...roleKeys.detail(id)],
    queryFn: () => rolesApi.getRole(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

// Existing mutations remain unchanged
export const useCreateRole = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  return useMutation({
    mutationFn: (role: CreateRoleData) => rolesApi.createRole(role),
    onSuccess: (newRole) => {
      // Invalidate all role queries since we don't have appName context
      queryClient.invalidateQueries({ queryKey: roleKeys.all });

      // Success toast
      toast.toast({
        title: "Success",
        description: `Role "${newRole.name}" created successfully!`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to create role:", error);

      // Error toast
      toast.toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to create role. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useUpdateRole = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (role: UpdateRoleData) => rolesApi.updateRole(role),
    onSuccess: (updatedRole) => {
      // Invalidate all role queries since we don't have appName context
      queryClient.invalidateQueries({ queryKey: roleKeys.all });

      // Success toast
      toast.toast({
        title: "Success",
        description: `Role "${updatedRole.name}" updated successfully!`,
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to update role:", error);

      // Error toast
      toast.toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to update role. Please try again.",
        variant: "destructive",
      });
    },
  });
};

export const useDeleteRole = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  return useMutation({
    mutationFn: (roleId: string) => rolesApi.deleteRole(roleId),
    onSuccess: () => {
      // Invalidate all role queries since we don't have appName context
      queryClient.invalidateQueries({ queryKey: roleKeys.all });

      // Success toast
      toast.toast({
        title: "Success",
        description: "Role deleted successfully!",
        variant: "default",
      });
    },
    onError: (error: any) => {
      console.error("Failed to delete role:", error);

      // Error toast
      toast.toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          error.message ||
          "Failed to delete role. Please try again.",
        variant: "destructive",
      });
    },
  });
};
