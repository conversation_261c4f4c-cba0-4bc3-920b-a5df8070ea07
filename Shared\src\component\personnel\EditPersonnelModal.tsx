
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';

interface Person {
  id: string;
  name: string;
  email: string;
  organization: string;
  role: string;
  isUser: boolean;
  hasLogin: boolean;
  status: 'Active' | 'Inactive' | 'Pending';
}

interface EditPersonnelModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  person: Person | null;
  onUpdate: (person: Person) => void;
}

const EditPersonnelModal: React.FC<EditPersonnelModalProps> = ({
  open,
  onOpenChange,
  person,
  onUpdate,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    role: '',
    isUser: false,
    status: 'Pending' as 'Active' | 'Inactive' | 'Pending',
  });

  const organizations = ['TechCorp Solutions', 'Global Industries', 'Innovation Labs', 'StartupXYZ'];

  useEffect(() => {
    if (person) {
      setFormData({
        name: person.name,
        email: person.email,
        organization: person.organization,
        role: person.role,
        isUser: person.isUser,
        status: person.status,
      });
    }
  }, [person]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!person) return;

    const updatedPerson: Person = {
      ...person,
      name: formData.name,
      email: formData.email,
      organization: formData.organization,
      role: formData.role,
      isUser: formData.isUser,
      status: formData.status,
    };

    onUpdate(updatedPerson);
    onOpenChange(false);
  };

  if (!person) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Personnel</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter full name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter email address"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="organization">Organization</Label>
            <Select
              value={formData.organization}
              onValueChange={(value) => setFormData({ ...formData, organization: value })}
              required
            >
              <SelectTrigger>
                <SelectValue placeholder="Select organization" />
              </SelectTrigger>
              <SelectContent>
                {organizations.map((org) => (
                  <SelectItem key={org} value={org}>
                    {org}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Input
              id="role"
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              placeholder="Enter job role"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value: 'Active' | 'Inactive' | 'Pending') =>
                setFormData({ ...formData, status: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Inactive">Inactive</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="isUser">System User</Label>
            <Switch
              id="isUser"
              checked={formData.isUser}
              onCheckedChange={(checked) => setFormData({ ...formData, isUser: checked })}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">Update Personnel</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditPersonnelModal;
