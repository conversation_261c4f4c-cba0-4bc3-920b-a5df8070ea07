// Helper to decode JWT and extract payload
const decodeJwtPayload = (token: string | null): any => {
  if (!token) return null;
  try {
    const payload = token.split(".")[1];
    const decoded = atob(payload.replace(/-/g, "+").replace(/_/g, "/"));
    return JSON.parse(decoded);
  } catch {
    return null;
  }
};

export const useDecodedJwt = () => {
  const token = localStorage.getItem("access_token");
  return decodeJwtPayload(token);
};
