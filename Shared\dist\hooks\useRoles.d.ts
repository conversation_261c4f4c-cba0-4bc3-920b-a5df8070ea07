import { Permission } from "../lib/permissionUtils";
export interface Role {
    _id?: string;
    name: string;
    description: string;
    permissionsList: Permission[];
}
export interface CreateRoleData {
    name: string;
    description: string;
    permissionsList: Permission[];
}
export interface UpdateRoleData extends CreateRoleData {
    id: string;
}
export declare const rolesApi: {
    getRoles: (appName: string) => Promise<any>;
    getRolesSimple: (appName: string) => Promise<any>;
    getRole: (id: string) => Promise<Role | null>;
    createRole: (data: CreateRoleData) => Promise<Role>;
    updateRole: (data: UpdateRoleData) => Promise<Role>;
    deleteRole: (id: string) => Promise<void>;
};
export declare const roleKeys: {
    all: readonly ["roles"];
    lists: () => readonly ["roles", "list"];
    list: () => readonly ["roles", "list"];
    details: () => readonly ["roles", "detail"];
    detail: (id: string) => readonly ["roles", "detail", string];
};
export declare const permissionKeys: {
    all: readonly ["permissions"];
    lists: () => readonly ["permissions", "list"];
    list: () => readonly ["permissions", "list"];
};
export declare const useRolesWithPagination: (appName: string) => import("@tanstack/react-query").UseQueryResult<any, Error>;
export declare const useRoles: (appName: string) => import("@tanstack/react-query").UseQueryResult<any, Error>;
export declare const useRole: (id: string) => import("@tanstack/react-query").UseQueryResult<Role | null, Error>;
export declare const useCreateRole: () => import("@tanstack/react-query").UseMutationResult<Role, any, CreateRoleData, unknown>;
export declare const useUpdateRole: () => import("@tanstack/react-query").UseMutationResult<Role, any, UpdateRoleData, unknown>;
export declare const useDeleteRole: () => import("@tanstack/react-query").UseMutationResult<void, any, string, unknown>;
