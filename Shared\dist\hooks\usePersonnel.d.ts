import { PersonnelRoles } from "../lib/Schemas/Personnel";
import { type Person, type ResetPassword } from "../lib/Schemas/Personnel";
export declare const personnelApi: {
    getPersonnel: () => Promise<any>;
    getPersonnelSimple: () => Promise<any>;
    createPersonnel: (data: Omit<Person, "id" | "status" | "hasLogin">) => Promise<Person>;
    updatePersonnel: (data: Partial<Person> & {
        id: string;
    }) => Promise<Person>;
    deletePersonnel: (id: string) => Promise<void>;
    toggleUserStatus: (id: string) => Promise<Person>;
    createLogin: (id: string) => Promise<Person>;
    getRoles: () => Promise<PersonnelRoles[]>;
    resetPassword: (id: string, data: {
        currentPassword: string;
        password: string;
    }) => Promise<any>;
};
export declare const personnelKeys: {
    all: readonly ["personnel"];
    lists: () => readonly ["personnel", "list"];
    list: () => readonly ["personnel", "list"];
    details: () => readonly ["personnel", "detail"];
    detail: (id: string) => readonly ["personnel", "detail", string];
};
export declare const usePersonnelWithPagination: () => import("@tanstack/react-query").UseQueryResult<any, Error>;
export declare const usePersonnel: () => import("@tanstack/react-query").UseQueryResult<any, Error>;
export declare const useCreatePersonnel: () => import("@tanstack/react-query").UseMutationResult<Person, any, Omit<Person, "id" | "status" | "hasLogin">, unknown>;
export declare const useUpdatePersonnel: () => import("@tanstack/react-query").UseMutationResult<Person, any, Partial<Person> & {
    id: string;
}, unknown>;
export declare const useDeletePersonnel: () => import("@tanstack/react-query").UseMutationResult<void, any, string, unknown>;
export declare const useToggleUserStatus: () => import("@tanstack/react-query").UseMutationResult<Person, any, string, unknown>;
export declare const useCreateLogin: () => import("@tanstack/react-query").UseMutationResult<Person, any, string, unknown>;
export declare const useRoles: () => import("@tanstack/react-query").UseQueryResult<PersonnelRoles[], Error>;
export declare const useResetPassword: () => import("@tanstack/react-query").UseMutationResult<any, any, ResetPassword & {
    id: string;
}, unknown>;
