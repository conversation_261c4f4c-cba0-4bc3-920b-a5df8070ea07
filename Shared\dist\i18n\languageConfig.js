// Common language sets
export const languages = [
    { code: 'en', name: 'English', nativeName: 'English', flag: 'us' },
    { code: 'es', name: 'Spanish', nativeName: 'Spanish', flag: 'es' },
];
// Standard React configuration
export const reactLanguageConfig = {
    defaultLanguage: 'en',
    storageKey: 'app-language',
    languages: languages,
    fallbackLanguage: 'en',
    detectBrowserLanguage: true,
};
// Custom configuration builder
export function createLanguageConfig(overrides = {}) {
    return {
        ...reactLanguageConfig,
        ...overrides,
    };
}
// Sample translations for common UI elements
export const commonTranslations = {
    en: {
        'header.title': 'Relativity Cloud Admin',
        'language.select': 'Select Language',
        'language.current': 'Current Language',
        'theme.select': 'Select Theme',
        'theme.light': 'Light',
        'theme.dark': 'Dark',
        'theme.system': 'System',
        'common.save': 'Save',
        'common.cancel': 'Cancel',
        'common.apply': 'Apply',
        'common.close': 'Close',
        'common.settings': 'Settings',
        'common.preferences': 'Preferences',
        'common.loading': 'Loading...',
        'common.retry': 'Retry',
        'common.error': 'Error es',
        'nav.home': 'Home',
        'nav.about': 'About',
        'nav.contact': 'Contact',
        'nav.help': 'Help',
        'nav.dashboard': 'Dashboard',
        'nav.personnel': 'Personnel',
        'nav.organizations': 'Organizations',
        'nav.roles': 'Roles',
        'dashboard.title': 'Dashboard Overview',
        'dashboard.subtitle': 'Monitor your multi-tenant client management system',
        'dashboard.totalOrgs': 'Total Organizations',
        'dashboard.activeUsers': 'Active Users',
        'dashboard.licenseRevenue': 'License Revenue',
        'dashboard.growthRate': 'Growth Rate',
        'dashboard.recentOrgs': 'Recent Organizations',
        'dashboard.licenseUsage': 'License Usage',
        'dashboard.licensesSold': 'Total Licenses Sold',
        'dashboard.utilizationRate': 'Utilization Rate',
        'dashboard.noRecentOrgs': 'No recent organizations',
        'dashboard.loading': 'Loading dashboard data...',
        'dashboard.noData': 'No dashboard data',
        'dashboard.error': 'Error loading dashboard data',
        'organizations.title': 'Organization Management',
        'organizations.subtitle': 'Manage your client organizations and their settings',
        'organizations.addOrganization': 'Add Organization',
        'organizations.addOrganizations': 'Add Organizations',
        'organizations.search': 'Search Organizations',
        'organizations.users': 'Users',
        'organizations.month': 'Month',
        'organizations.viewDetails': 'View Details',
        'organizations.manage': 'Manage',
        'organizations.details': 'Details',
        'organizations.orgId': 'Organization ID',
        'organizations.domain': 'Domain',
        'organizations.domainPlaceholder': 'example.com',
        'organizations.createdDate': 'Created Date',
        'organizations.contactEmail': 'Contact Email',
        'organizations.lastLogin': 'Last Login',
        'organizations.usageStats': 'Usage Statistics',
        'organizations.monthlyRevenue': 'Monthly Revenue',
        'organizations.annualRevenue': 'Annual Revenue',
        'organizations.licenseUtilization': 'License Utilization',
        'organizations.license': 'License',
        'organizations.licenses': 'Licenses',
        'organizations.current': 'Current',
        'organizations.currentBillingPeriod': 'Current billing period',
        'organizations.currentUsage': 'Current usage',
        'organizations.billingInformation': 'Billing Information',
        'organizations.billingCycle': 'Billing Cycle',
        'organizations.nextBillingDate': 'Next Billing Date',
        'organizations.paymentMethod': 'Payment Method',
        'organizations.totalLicenses': 'Total Licenses',
        'organizations.status': 'Status',
        'organizations.status.active': 'Active',
        'organizations.status.pending': 'Pending',
        'organizations.status.suspended': 'Suspended',
        'organizations.organizationName': 'Organization Name',
        'organizations.organizationNamePlaceHolder': 'Enter Organization Name',
        'organizations.numberOfLicenses': 'Number of Licenses',
        'organizations.addressInformation': 'Address Information',
        'organizations.streetAddress': 'Street Address',
        'organizations.streetAddressPlaceholder': '123 Main Street',
        'organizations.city': 'City',
        'organizations.cityPlaceholder': 'Select City',
        'organizations.state': 'State',
        'organizations.statePlaceholder': 'Select State',
        'organizations.zipcode': 'Zip Code',
        'organizations.zipcodePlaceholder': 'Select Zip Code',
        'organizations.country': 'Country',
        'organizations.countryPlaceholder': 'Select Country',
        'organizations.description': 'Description (Optional)',
        'organizations.descriptionPlaceholder': 'Brief description of the organization',
        'organizations.creating': 'Creating',
        'organizations.general': 'General',
        'organizations.2fa': '2FA',
        'organizations.delete': 'Delete Organization',
        'organizations.deleteMessage': 'Are you sure you want to delete this organization? This action cannot be undone.',
        'organizations.deleteConfirm': 'Yes, Delete',
        'license.title': 'Select Applications & Number of Licenses',
        'license.subtitle': 'Choose applications and specify the number of user licenses needed.',
        'license.appsSelected': 'Applications Selected',
        'license.totalLicenses': 'Total Number of Licenses',
        'license.numOfLicenses': 'Number of Licenses',
        'license.selectedApps': 'Selected Applications: ',
        'license.noPermission': 'No Permission to view License Management',
        'license.availableLicenses': 'Available Licenses',
        'license.monthlyCost': 'Monthly Cost',
        'license.applicationLicenses': 'Application Licenses',
        'userMgmt.title': 'User Management',
        'userMgmt.addUser': 'Add New User',
        'userMgmt.percentLicensesUsed': '% of licenses used',
        'userMgmt.exportUserList': 'Export User List',
        'userMgmt.bulkUserOperations': 'Bulk User Operationst',
        'userMgmt.userAccessReport': 'User Access Report',
        '2fa.multiAuth': 'Multi-Factor Authentication',
        '2fa.enable2FA': 'Enable 2FA',
        '2fa.authenticationMethods': 'Authentication Methods',
        'billing.billingActions': 'Billing Actions',
        'billing.generateInvoice': 'Generate Invoice',
        'billing.paymentHistory': 'Payment History',
        'billing.updateBillingInfo': 'Update Billing Info',
        'personnel.title': 'Personnel Management',
        'personnel.subtitle': 'Manage personnel records and user access',
        'personnel.search': 'Search Personnel',
        'personnel.error': "Failed to load personnel data",
        'personnel.retry': "Retry",
        'personnel.addPersonnel': 'Add Personnel',
        'personnel.noPermission': "You do not have permission to view personnel.",
        'personnel.allOrganizations': "All Organizations",
        'personnel.firstName': 'First Name',
        'personnel.lastName': 'Last Name',
        'personnel.email': 'Email',
        'personnel.phoneNumber': 'Phone Number',
        'personnel.organization': 'Organization',
        'personnel.roleSingle': 'Role (Select One)',
        'personnel.created': 'Created',
        'personnel.updated': 'Updated',
        'personnel.edit': 'Edit',
        'personnel.noPersonnelFound': 'No personnel found matching your criteria',
        'personnel.autoSelectedFor': 'Auto-selected for es',
        'personnel.selected': 'Selected',
        'personnel.oneRoleAllowed': 'Only one role allowed for Relativity Admin',
        'personnel.applications': 'Applications',
        'personnel.noApplicationsAvailable': 'No applications available',
        'personnel.selectPermissions': 'Select Permissions',
        'personnel.saving': 'Saving...',
        'personnel.adding': 'Adding...',
        'personnel.roleSelectOne': 'Role (Select One)',
        'personnel.roleSelectMultiple': 'Roles (Select Multiple)',
        'personnel.addNewPersonnel': 'Add New Personnel',
        'personnel.editPersonnel': 'Edit Personnel',
        'personnel.delete': 'Delete Personnel',
        'personnel.deleteMessage': 'Are you sure you want to delete this personnel? This action cannot be undone.',
        'personnel.deleteConfirm': 'Yes, Delete',
        'personnel.changePassword': 'Change Password',
        'personnel.passwordPlaceholder': 'Password',
        'personnel.confirmPasswordPlaceholder': ' Confirm Password',
        'roles.title': 'Roles & Permissions',
        'roles.error': 'Error loading roles',
        'roles.loading': 'Loading...',
        'roles.create': 'Create Role',
        'roles.edit': 'Edit Role',
        'roles.update': 'Update Role',
        'roles.roleName': 'Role Name',
        'roles.roleNamePlaceHolder': 'Enter role name',
        'roles.description': 'Description',
        'roles.descriptionPlaceholder': 'Enter role description',
        'roles.permissions': 'Permissions',
        'roles.personnel': 'Personnel',
        'roles.organization': 'Organizations',
        'roles.hierarchy': 'Hierarchy',
        'roles.position': 'Position',
        'roles.analytics': 'Analytics',
        'roles.billing': 'Billing',
        'roles.schedule': 'Schedule',
        'roles.shift': 'Shift',
        'roles.attendaceTracking': 'Attendance Tracking',
        'roles.attendanceReports': 'Attendance Reports',
        'roles.report': 'Report',
        'roles.alertRule': 'Alert Rule',
        'roles.alertAndReportHistory': 'Alert and Report History',
        'roles.noRolesAvailable': 'No roles available',
        'roles.delete': 'Delete Role',
        'roles.deleteMessage': 'Are you sure you want to delete this role? This action cannot be undone.',
        'roles.deleteConfirm': 'Yes, Delete',
        // Home Keys
        'home.title': 'Relativity Cloud Home',
        'home.welcome': 'Welcome',
        'home.chooseApp': 'Choose an app to get started.',
        'home.searchPlaceholder': 'Search apps... ',
        'home.noAppsFound': 'No Applications Found',
        'home.open': 'Open',
        'home.new': 'New',
        'home.recent': 'Recent',
        'home.list': 'List',
        'home.grid': 'Grid',
        'home.admin': 'Admin',
        'home.application': 'Application',
        // Organization Keys
        'org.title': 'Organization Admin Portal',
        'org.subtitle': 'Comprehensive management dashboard for your organization',
        'org.licenseManagement': 'License Management',
        'org.personnel': 'Personnel',
        'org.organizationInfo': 'Organization Info',
        'org.analytics': 'Analytics',
        'org.reportsAlerts': 'Reports & Alerts',
        'org.hierarchyRoles': 'Hierarchy & Roles',
        'org.scheduleAttendance': 'Schedule & Attendance',
        'org.back': 'Back',
        'org.noPermission': 'No Permission to view organizations',
        'org.organizationInformation': 'Organization Information',
        'org.organizationSubInformation': "Manage your organization's profile and contact details",
        'org.basicInformation': "Basic Information",
        'org.contactInformation': 'Contact Information',
        'org.headquartersAddress': 'Headquarters Address',
        'org.streetAddress': 'Street Address',
        'org.stateProvince': 'State / Province',
        'org.zipPostalCode': 'ZIP/Postal Code',
        'org.legalInformation': 'Legal Information',
        'org.accessPortal': 'Access Portal',
        'org.orgAdminPortal': 'OrgAdmin Portal',
        'org.subheading': 'Enterprise Management Platform',
        'org.enterpiseGradeManagement': 'Enterprise-Grade Management',
        'org.organizationManagementPortal': 'Organization Admin Portal',
        'org.organizationManagementPortalInfo': 'Comprehensive management platform for organizational licenses, personnel, schedules, and analytics. Streamline your enterprise operations with powerful tools and insights.',
        'org.launchAdminPortal': 'Launch Admin Portal',
        'org.viewDocumentation': 'View Documentation',
        'org.comprehensiveManagementCapabilities': 'Comprehensive Management Capabilities',
        'org.launchPortalNow': 'Launch Portal Now',
        'org.readyToGetStarted': 'Ready to Get Started?',
        // Organization Analytics Screen
        'analytics.title': 'Analytics & Trends',
        'analytics.subtitle': 'Monitor usage patterns and organizational insights',
        'analytics.noPermission': 'No Permission to view Analytics',
        'analytics.last7days': 'Last 7 days',
        'analytics.last30days': 'Last 30 days',
        'analytics.last90days': 'Last 90 days',
        'analytics.lastYear': 'Last year',
        'analytics.fromLastMonth': 'from last month',
        'analytics.tokenUsage': 'Token Usage',
        'analytics.licenseCost': 'License Cost',
        'analytics.averageSessionTime': 'Avg Session Time',
        'analytics.licenseUsageTrends': 'License Usage Trends',
        'analytics.userActivity': 'User Activity',
        'analytics.departmentUsageDistribution': 'Department Usage Distribution',
        'analytics.costVsBudgetTrend': 'Cost vs Budget Trend',
        'analytics.usageInsights': 'Usage Insights',
        'analytics.topPerformingApplications': 'Top Performing Applications',
        'analytics.peakUsageTimes': 'Peak Usage Times',
        'analytics.optimizationOpportunities': 'Optimization Opportunities',
        // Organization Reports and alerts screen
        'reports.title': 'Reports, Alerts & Reminders',
        'reports.subtitle': 'Manage automated reports and system notifications',
        'reports.newAlertRule': 'New Alert Rule',
        'reports.newReport': 'New Report',
        'reports.createAlertRule': 'Create Alert Rule',
        'reports.createNewReport': 'Create New Report',
        'reports.createReport': 'Create Report',
        'reports.alertName': 'Alert Name',
        'reports.triggerCondition': 'Trigger Condition',
        'reports.threshold': 'Threshold',
        'reports.reportName': 'Report Name',
        'reports.reportType': 'Report Type',
        'reports.schedule': 'Schedule',
        'reports.recipients': 'Recipients',
        'reports.activeAlerts': 'Active Alerts',
        'reports.scheduledReports': 'Scheduled Reports',
        'reports.history': 'History',
        'reports.highPriority': 'High Priority',
        'reports.resolvedToday': 'Resolved Today',
        'reports.systemAlerts': 'System Alerts',
        'reports.noPermissionActiveAlerts': 'No Permission to view Active Alerts',
        'reports.download': 'Download',
        'reports.lastGenerated': 'Last Generated',
        'reports.format': 'Format',
        'reports.people': 'people',
        'reports.noPermissionScheduledReports': 'No Permission to view Scheduled Reports',
        'reports.alertAndReportHistory': 'Alert & Report History',
        'reports.historicalAlerts': 'Historical alerts and report generation logs',
        'reports.noPermissionHistory': 'No Permission to view History',
        // Organization and hierarchy roles screen
        'orgHierarchy.title': 'Organizational Hierarchy & Roles',
        'orgHierarchy.subtitle': 'Manage organizational structure and user permissions',
        'orgHierarchy.organizationalHierarchy': 'Organizational Hierarchy',
        'orgHierarchy.rolesAndPermissions': 'Roles & Permissions',
        'orgHierarchy.positionAndAssignments': 'Position Assignments',
        'orgHierarchy.noPermissionPositionAssignment': 'No Permission to view Positions Assignments',
        'orgHierarchy.positionsFilled': 'positions filled',
        'orgHierarchy.vacantPosition': 'Vacant Position',
        'orgHierarchy.totalDepartments': 'Total Departments',
        'orgHierarchy.totalPositions': 'Total Positions',
        'orgHierarchy.filledPositions': 'Filled Positions',
        'orgHierarchy.fillRate': 'Fill Rate',
        'orgHierarchy.departmentStructure': 'Department Structure',
        'orgHierarchy.addDepartment': 'Add Department',
        'orgHierarchy.editDepartment': 'Edit Department',
        'orgHierarchy.deleteDepartment': 'Delete Department',
        'orgHierarchy.deleteDepartmentDescription': 'Are you sure you want to delete this department? This action cannot be undone and will remove all sub-departments and positions.',
        'orgHierarchy.departmentName': 'Department Name',
        'orgHierarchy.parentDepartment': 'Parent Department',
        'orgHierarchy.totalRoles': 'Total Roles',
        'orgHierarchy.customRoles': 'Custom Roles',
        'orgHierarchy.adminUsers': 'Admin Users',
        'orgHierarchy.addPosition': 'Add Position',
        'orgHierarchy.positionTitle': 'Position Title',
        'orgHierarchy.department': 'Department',
        'orgHierarchy.assignedPersonnel': 'Assigned Personnel',
        'orgHierarchy.actions': 'Actions',
        'orgHierarchy.unassigned': 'Unassigned',
        'orgHierarchy.unassign.title': 'Confirm Unassign',
        'orgHierarchy.unassign.description': 'Are you sure you want to unassign this position? This action cannot be undone.',
        'orgHierarchy.unassign.confirmText': 'Yes, Unassign',
        'orgHierarchy.position.addPosition': 'Add Position',
        'orgHierarchy.position.selectRoles': 'Select Roles',
        // Organization Schedule and attendance screen
        'scheduleAttendace.title': 'Schedule & Attendance Management',
        'scheduleAttendace.subtitle': 'Manage organizational shifts, schedules, and attendance tracking',
        'scheduleAttendace.createShift': 'Create Shift',
        'scheduleAttendace.createNewShift': 'Create New Shift',
        'scheduleAttendace.scheduleEmployee': 'Schedule Employee',
        'scheduleAttendace.shiftName': 'Shift Name',
        'scheduleAttendace.startTime': 'Start Time',
        'scheduleAttendace.endTime': 'End Time',
        'scheduleAttendace.capacity': 'Capacity',
        'scheduleAttendace.employee': 'Employee',
        'scheduleAttendace.date': 'Date',
        'scheduleAttendace.recurrence': 'Recurrence',
        'scheduleAttendace.totalScheduled': 'Total Scheduled',
        'scheduleAttendace.present': 'Present',
        'scheduleAttendace.absent': 'Absent',
        'scheduleAttendace.attendaceRate': 'Attendance Rate',
        'scheduleAttendace.todaysSchedule': "Today's Schedule",
        'scheduleAttendace.shiftManagement': "Shift Management",
        'scheduleAttendace.attendanceTracking': "Attendance Tracking",
        'scheduleAttendace.attendanceReports': "Attendance Reports",
        'scheduleAttendace.dailySchedule': "Daily Schedule",
        'scheduleAttendace.checkIn': "Check-in",
        'scheduleAttendace.checkOut': "Check-out",
        'scheduleAttendace.noPermission': "No Permission to view Schedules",
        'scheduleAttendace.assigned': "Assigned",
        'scheduleAttendace.utilization': "Utilization",
        'scheduleAttendace.noPermissionShifts': "No Permission to view Shifts",
        'scheduleAttendace.attendanceTrackingTitle': "Real-time attendance tracking and management",
        'scheduleAttendace.noPermissionAttendanceTracking': "No Permission to view Attendance Tracking",
        'scheduleAttendace.attendanceReportsTitle': "Attendance Reports",
        'scheduleAttendace.attendanceReportsSubTitle': "Comprehensive attendance reports and analytics",
        'scheduleAttendace.noPermissionAttendanceReports': "No Permission to view Attendance Reports",
    },
    es: {
        'header.title': 'Relativity Cloud Admin es',
        'language.select': 'Seleccionar Idioma',
        'language.current': 'Idioma Actual',
        'theme.select': 'Seleccionar Tema',
        'theme.light': 'Claro',
        'theme.dark': 'Oscuro',
        'theme.system': 'Sistema',
        'common.save': 'Guardar',
        'common.cancel': 'Cancelar',
        'common.apply': 'Aplicar',
        'common.close': 'Cerrar',
        'common.settings': 'Configuración',
        'common.preferences': 'Preferencias',
        'common.loading': 'Loading... es',
        'common.retry': 'Retry es',
        'common.error': 'Error es',
        'nav.home': 'Inicio',
        'nav.about': 'Acerca de',
        'nav.contact': 'Contacto',
        'nav.help': 'Ayuda',
        'nav.dashboard': 'Dashboard es',
        'nav.personnel': 'Personnel es',
        'nav.organizations': 'Organizations es',
        'nav.roles': 'Roles es',
        'dashboard.title': 'Dashboard Overview es',
        'dashboard.subtitle': 'Monitor your multi-tenant client management system es',
        'dashboard.totalOrgs': 'Total Organizations es',
        'dashboard.activeUsers': 'Active Users es',
        'dashboard.licenseRevenue': 'License Revenue es',
        'dashboard.growthRate': 'Growth Rate es',
        'dashboard.recentOrgs': 'Recent Organizations es',
        'dashboard.licenseUsage': 'License Usage es',
        'dashboard.licensesSold': 'Total Licenses Sold es',
        'dashboard.utilizationRate': 'Utilization Rate es',
        'dashboard.noRecentOrgs': 'No recent organizations es',
        'dashboard.loading': 'Loading dashboard data... es',
        'dashboard.error': 'Error loading dashboard data es',
        'dashboard.noData': 'No dashboard data es',
        'organizations.title': 'Organization Management es',
        'organizations.subtitle': 'Manage your client organizations and their settings es',
        'organizations.addOrganization': 'Add Organization es',
        'organizations.addOrganizations': 'Add Organizations es',
        'organizations.search': 'Search Organizations es',
        'organizations.users': 'Users es',
        'organizations.month': 'Month es',
        'organizations.viewDetails': 'View Details es',
        'organizations.manage': 'Manage es',
        'organizations.details': 'Details es',
        'organizations.orgId': 'Organization ID es',
        'organizations.domain': 'Domain es',
        'organizations.domainPlaceholder': 'example.com es',
        'organizations.createdDate': 'Created Date es',
        'organizations.contactEmail': 'Contact Email es',
        'organizations.lastLogin': 'Last Login es',
        'organizations.usageStats': 'Usage Statistics es',
        'organizations.monthlyRevenue': 'Monthly Revenue es',
        'organizations.annualRevenue': 'Annual Revenue es',
        'organizations.licenseUtilization': 'License Utilization es',
        'organizations.license': 'License es',
        'organizations.licenses': 'Licenses es',
        'organizations.current': 'Current es',
        'organizations.currentBillingPeriod': 'Current billing period es',
        'organizations.currentUsage': 'Current usage es',
        'organizations.billingInformation': 'Billing Information es',
        'organizations.billingCycle': 'Billing Cycle es',
        'organizations.nextBillingDate': 'Next Billing Date es',
        'organizations.paymentMethod': 'Payment Method es',
        'organizations.totalLicenses': 'Total Licenses es',
        'organizations.status': 'Status es',
        'organizations.status.active': 'Active es',
        'organizations.status.pending': 'Pending es',
        'organizations.status.suspended': 'Suspended es',
        'organizations.organizationName': 'Organization Name es',
        'organizations.organizationNamePlaceHolder': 'Enter Organization Name es',
        'organizations.numberOfLicenses': 'Number of Licenses es',
        'organizations.addressInformation': 'Address Information es',
        'organizations.streetAddress': 'Street Address es',
        'organizations.streetAddressPlaceholder': '123 Main Street es',
        'organizations.city': 'City es',
        'organizations.cityPlaceholder': 'Select City es',
        'organizations.state': 'State es',
        'organizations.statePlaceholder': 'Select State es',
        'organizations.zipcode': 'Zip Code es',
        'organizations.zipcodePlaceholder': 'Select Zip Code es',
        'organizations.country': 'Country es',
        'organizations.countryPlaceholder': 'Select Country es',
        'organizations.description': 'Description (Optional) es',
        'organizations.descriptionPlaceholder': 'Brief description of the organization es',
        'organizations.creating': 'Creating es',
        'organizations.general': 'General es',
        'organizations.2fa': '2FA es',
        'organizations.delete': 'Delete Organization es',
        'organizations.deleteMessage': 'Are you sure you want to delete this organization? This action cannot be undone. es',
        'organizations.deleteConfirm': 'Yes, Delete es',
        'license.title': 'Select Applications & Number of Licenses es',
        'license.subtitle': 'Choose applications and specify the number of user licenses needed. es',
        'license.appsSelected': 'Applications Selected es',
        'license.totalLicenses': 'Total Number of Licenses es',
        'license.numOfLicenses': 'Number of Licenses es',
        'license.selectedApps': 'Selected Applications es:',
        'license.noPermission': 'No Permission to view License Management es',
        'license.availableLicenses': 'Available Licenses es',
        'license.monthlyCost': 'Monthly Cost es',
        'license.applicationLicenses': 'Application Licenses es',
        'userMgmt.title': 'User Management es',
        'userMgmt.addUser': 'Add New User es',
        'userMgmt.percentLicensesUsed': '% of licenses used es',
        'userMgmt.exportUserList': 'Export User List es',
        'userMgmt.bulkUserOperations': 'Bulk User Operations es',
        'userMgmt.userAccessReport': 'User Access Report es',
        'billing.billingActions': 'Billing Actions es',
        'billing.generateInvoice': 'Generate Invoice es',
        'billing.paymentHistory': 'Payment History es',
        'billing.updateBillingInfo': 'Update Billing Info es',
        '2fa.multiAuth': 'Multi-Factor Authentication es',
        '2fa.enable2FA': 'Enable 2FA es',
        '2fa.authenticationMethods': 'Authentication Methods es',
        'personnel.title': 'Personnel Management es',
        'personnel.subtitle': 'Manage personnel records and user access es',
        'personnel.search': 'Search Personnel es',
        'personnel.error': "Failed to load personnel data es",
        'personnel.retry': "Retry es",
        'personnel.noPermission': "You do not have permission to view personnel. es",
        'personnel.allOrganizations': "All Organizations",
        'personnel.addPersonnel': 'Add Personnel es',
        'personnel.firstName': 'First Name es',
        'personnel.lastName': 'Last Name es',
        'personnel.email': 'Email es',
        'personnel.phoneNumber': 'Phone Number es',
        'personnel.organization': 'Organization es',
        'personnel.roleSingle': 'Role (Select One) es',
        'personnel.created': 'Created es',
        'personnel.updated': 'Updated es',
        'personnel.edit': 'Edit es',
        'personnel.noPersonnelFound': 'No personnel found matching your criteria es',
        'personnel.autoSelectedFor': 'Auto-selected for es',
        'personnel.selected': 'Selected es',
        'personnel.oneRoleAllowed': 'Only one role allowed for Relativity Admin es',
        'personnel.applications': 'Applications es',
        'personnel.noApplicationsAvailable': 'No applications available es',
        'personnel.selectPermissions': 'Select Permissions es',
        'personnel.saving': 'Saving... es',
        'personnel.adding': 'Adding... es',
        'personnel.roleSelectOne': 'Role (Select One) es',
        'personnel.roleSelectMultiple': 'Roles (Select Multiple) es',
        'personnel.addNewPersonnel': 'Add New Personnel es',
        'personnel.editPersonnel': 'Edit Personnel es',
        'personnel.delete': 'Delete Personnel es',
        'personnel.deleteMessage': 'Are you sure you want to delete this personnel? This action cannot be undone. es',
        'personnel.deleteConfirm': 'Yes, Delete es',
        'personnel.changePassword': 'Change Password es',
        'personnel.passwordPlaceholder': 'Password es',
        'personnel.confirmPasswordPlaceholder': 'Confirm Password es',
        'roles.title': 'Roles & Permissions es',
        'roles.error': 'Error loading roles es',
        'roles.loading': 'Loading... es',
        'roles.create': 'Create Role es',
        'roles.edit': 'Edit Role es',
        'roles.update': 'Update Role es',
        'roles.roleName': 'Role Name es',
        'roles.roleNamePlaceholder': 'Enter role name es',
        'roles.description': 'Description es',
        'roles.descriptionPlaceholder': 'Enter role description es',
        'roles.permissions': 'Permissions es',
        'roles.personnel': 'Personnel es',
        'roles.organization': 'Organizations es',
        'roles.hierarchy': 'Hierarchy es',
        'roles.position': 'Position es',
        'roles.analytics': 'Analytics es',
        'roles.billing': 'Billing es',
        'roles.schedule': 'Schedule es',
        'roles.shift': 'Shift es',
        'roles.attendaceTracking': 'Attendance Tracking es',
        'roles.attendanceReports': 'Attendance Reports es',
        'roles.report': 'Report es',
        'roles.alertRule': 'Alert Rule es',
        'roles.alertAndReportHistory': 'Alert and Report History es',
        'roles.noRolesAvailable': 'No roles available es',
        'roles.delete': 'Delete Role es',
        'roles.deleteMessage': 'Are you sure you want to delete this role? This action cannot be undone. es',
        'roles.deleteConfirm': 'Yes, Delete es',
        // Home Keys
        'home.title': 'Relativity Cloud Home es',
        'home.welcome': 'Welcome es',
        'home.chooseApp': 'Choose an app to get started. es',
        'home.searchPlaceholder': 'Search apps... es',
        'home.noAppsFound': 'No Applications Found es',
        'home.open': 'Open es',
        'home.new': 'New es',
        'home.recent': 'Recent es',
        'home.list': 'List es',
        'home.grid': 'Grid es',
        'home.admin': 'Admin es',
        'home.application': 'Application es',
        // Organization Keys
        'org.title': 'Organization Admin Portal es',
        'org.subtitle': 'Comprehensive management dashboard for your organization es',
        'org.licenseManagement': 'License Management es',
        'org.personnel': 'Personnel es',
        'org.organizationInfo': 'Organization Info es',
        'org.analytics': 'Analytics es',
        'org.reportsAlerts': 'Reports & Alerts es',
        'org.hierarchyRoles': 'Hierarchy & Roles es',
        'org.scheduleAttendance': 'Schedule & Attendance es',
        'org.back': 'Back es',
        'org.noPermission': 'No Permission to view organizations es',
        'org.organizationInformation': 'Organization Information es',
        'org.organizationSubInformation': "Manage your organization's profile and contact details es",
        'org.basicInformation': 'Basic Information es',
        'org.contactInformation': 'Contact Information es',
        'org.headquartersAddress': 'Headquarters Address es',
        'org.streetAddress': 'Street Address es',
        'org.stateProvince': 'State / Province es',
        'org.zipPostalCode': 'ZIP/Postal Code es',
        'org.legalInformation': 'Legal Information es',
        'org.accessPortal': 'Access Portal es',
        'org.orgAdminPortal': 'OrgAdmin Portal es',
        'org.subheading': 'Enterprise Management Platform es',
        'org.enterpiseGradeManagement': 'Enterprise-Grade Management es',
        'org.organizationManagementPortal': 'Organization Admin Portal es',
        'org.organizationManagementPortalInfo': 'Comprehensive management platform for organizational licenses, personnel, schedules, and analytics. Streamline your enterprise operations with powerful tools and insights. es',
        'org.launchAdminPortal': 'Launch Admin Portal es',
        'org.viewDocumentation': 'View Documentation es',
        'org.comprehensiveManagementCapabilities': 'Comprehensive Management Capabilities es',
        'org.launchPortalNow': 'Launch Portal Now es',
        'org.readyToGetStarted': 'Ready to Get Started? es',
        // Organization Analytics screen
        'analytics.title': 'Analytics & Trends es',
        'analytics.subtitle': 'Monitor usage patterns and organizational insights es',
        'analytics.noPermission': 'No Permission to view Analytics es',
        'analytics.last7days': 'Last 7 days es',
        'analytics.last30days': 'Last 30 days es',
        'analytics.last90days': 'Last 90 days es',
        'analytics.lastYear': 'Last year es',
        'analytics.fromLastMonth': 'from last month es',
        'analytics.tokenUsage': 'Token Usage es',
        'analytics.licenseCost': 'License Cost es',
        'analytics.averageSessionTime': 'Avg Session Time es',
        'analytics.licenseUsageTrends': 'License Usage Trends es',
        'analytics.userActivity': 'User Activity es',
        'analytics.departmentUsageDistribution': 'Department Usage Distribution es',
        'analytics.costVsBudgetTrend': 'Cost vs Budget Trend es',
        'analytics.usageInsights': 'Usage Insights es',
        'analytics.topPerformingApplications': 'Top Performing Applications es',
        'analytics.peakUsageTimes': 'Peak Usage Times es',
        'analytics.optimizationOpportunities': 'Optimization Opportunities es',
        // Organization Reports and alerts screen
        'reports.title': 'Reports, Alerts & Reminders es',
        'reports.subtitle': 'Manage automated reports and system notifications es',
        'reports.newAlertRule': 'New Alert Rule es',
        'reports.newReport': 'New Report es',
        'reports.createAlertRule': 'Create Alert Rule es',
        'reports.createNewReport': 'Create New Report es',
        'reports.createReport': 'Create Report es',
        'reports.alertName': 'Alert Name es',
        'reports.triggerCondition': 'Trigger Condition es',
        'reports.threshold': 'Threshold es',
        'reports.reportName': 'Report Name es',
        'reports.reportType': 'Report Type es',
        'reports.schedule': 'Schedule es',
        'reports.recipients': 'Recipients es',
        'reports.activeAlerts': 'Active Alerts es',
        'reports.scheduledReports': 'Scheduled Reports es',
        'reports.history': 'History es',
        'reports.highPriority': 'High Priority es',
        'reports.resolvedToday': 'Resolved Today es',
        'reports.systemAlerts': 'System Alerts es',
        'reports.noPermissionActiveAlerts': 'No Permission to view Active Alerts es',
        'reports.download': 'Download es',
        'reports.lastGenerated': 'Last Generated es',
        'reports.format': 'Format es',
        'reports.people': 'people es',
        'reports.noPermissionScheduledReports': 'No Permission to view Scheduled Reports es',
        'reports.alertAndReportHistory': 'Alert & Report History es',
        'reports.historicalAlerts': 'Historical alerts and report generation logs es',
        'reports.noPermissionHistory': 'No Permission to view History es',
        // Organization and hierarchy roles screen
        'orgHierarchy.title': 'Organizational Hierarchy & Roles es',
        'orgHierarchy.subtitle': 'Manage organizational structure and user permissions es',
        'orgHierarchy.organizationalHierarchy': 'Organizational Hierarchy es',
        'orgHierarchy.rolesAndPermissions': 'Roles & Permissions es',
        'orgHierarchy.positionAndAssignments': 'Position Assignments es',
        'orgHierarchy.noPermissionPositionAssignment': 'No Permission to view Positions Assignments es',
        'orgHierarchy.positionsFilled': 'positions filled es',
        'orgHierarchy.vacantPosition': 'Vacant Position es',
        'orgHierarchy.totalDepartments': 'Total Departments es',
        'orgHierarchy.totalPositions': 'Total Positions es',
        'orgHierarchy.filledPositions': 'Filled Positions es',
        'orgHierarchy.fillRate': 'Fill Rate es',
        'orgHierarchy.departmentStructure': 'Department Structure es',
        'orgHierarchy.addDepartment': 'Add Department es',
        'orgHierarchy.editDepartment': 'Edit Department es',
        'orgHierarchy.deleteDepartment': 'Delete Department es',
        'orgHierarchy.deleteDepartmentDescription': 'Are you sure you want to delete this department? This action cannot be undone and will remove all sub-departments and positions. es',
        'orgHierarchy.departmentName': 'Department Name es',
        'orgHierarchy.parentDepartment': 'Parent Department es',
        'orgHierarchy.totalRoles': 'Total Roles es',
        'orgHierarchy.customRoles': 'Custom Roles es',
        'orgHierarchy.adminUsers': 'Admin Users es',
        'orgHierarchy.addPosition': 'Add Position es',
        'orgHierarchy.positionTitle': 'Position Title es',
        'orgHierarchy.department': 'Department es',
        'orgHierarchy.assignedPersonnel': 'Assigned Personnel es',
        'orgHierarchy.actions': 'Actions es',
        'orgHierarchy.unassigned': 'Unassigned es',
        'orgHierarchy.unassign.title': 'Confirm Unassign es',
        'orgHierarchy.unassign.description': 'Are you sure you want to unassign this position? This action cannot be undone. es',
        'orgHierarchy.unassign.confirmText': 'Yes, Unassign es',
        'orgHierarchy.position.addPosition': 'Add Position es',
        'orgHierarchy.position.selectRoles': 'Select Roles es',
        // Organization Schedule and attendance screen
        'scheduleAttendace.title': 'Schedule & Attendance Management es',
        'scheduleAttendace.subtitle': 'Manage organizational shifts, schedules, and attendance tracking es',
        'scheduleAttendace.createShift': 'Create Shift es',
        'scheduleAttendace.createNewShift': 'Create New Shift es',
        'scheduleAttendace.scheduleEmployee': 'Schedule Employee es',
        'scheduleAttendace.shiftName': 'Shift Name es',
        'scheduleAttendace.startTime': 'Start Time es',
        'scheduleAttendace.endTime': 'End Time es',
        'scheduleAttendace.capacity': 'Capacity es',
        'scheduleAttendace.employee': 'Employee es',
        'scheduleAttendace.date': 'Date es',
        'scheduleAttendace.recurrence': 'Recurrence es',
        'scheduleAttendace.totalScheduled': 'Total Scheduled es',
        'scheduleAttendace.present': 'Present es',
        'scheduleAttendace.absent': 'Absent es',
        'scheduleAttendace.attendaceRate': 'Attendance Rate es',
        'scheduleAttendace.todaysSchedule': "Today's Schedule es",
        'scheduleAttendace.shiftManagement': "Shift Management es",
        'scheduleAttendace.attendanceTracking': "Attendance Tracking es",
        'scheduleAttendace.attendanceReports': "Attendance Reports es",
        'scheduleAttendace.dailySchedule': "Daily Schedule es",
        'scheduleAttendace.checkIn': "Check-in es",
        'scheduleAttendace.checkOut': "Check-out es",
        'scheduleAttendace.noPermission': "No Permission to view Schedules es",
        'scheduleAttendace.assigned': "Assigned es",
        'scheduleAttendace.utilization': "Utilization es",
        'scheduleAttendace.noPermissionShifts': "No Permission to view Shifts es",
        'scheduleAttendace.attendanceTrackingTitle': "Real-time attendance tracking and management es",
        'scheduleAttendace.noPermissionAttendanceTracking': "No Permission to view Attendance Tracking es",
        'scheduleAttendace.attendanceReportsTitle': "Attendance Reports es",
        'scheduleAttendace.attendanceReportsSubTitle': "Comprehensive attendance reports and analytics es",
        'scheduleAttendace.noPermissionAttendanceReports': "No Permission to view Attendance Reports es",
    },
};
export default {
    languages,
    createLanguageConfig,
    commonTranslations,
};
