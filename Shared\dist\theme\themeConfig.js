// Predefined theme configurations for different frameworks
// Standard React configuration
export const reactThemeConfig = {
    defaultTheme: 'system',
    storageKey: 'app-theme',
    enableSystem: true,
    disableTransitionOnChange: false,
    themes: ['light', 'dark'],
    attribute: 'class',
};
// Next.js configuration
export const nextThemeConfig = {
    defaultTheme: 'system',
    storageKey: 'next-theme',
    enableSystem: true,
    disableTransitionOnChange: true, // Prevent flash on SSR
    themes: ['light', 'dark'],
    attribute: 'class',
};
// Vite/Create React App configuration
export const viteThemeConfig = {
    defaultTheme: 'system',
    storageKey: 'vite-theme',
    enableSystem: true,
    disableTransitionOnChange: false,
    themes: ['light', 'dark'],
    attribute: 'class',
};
// Tailwind CSS configuration
export const tailwindThemeConfig = {
    defaultTheme: 'system',
    storageKey: 'tailwind-theme',
    enableSystem: true,
    disableTransitionOnChange: false,
    themes: ['light', 'dark'],
    attribute: 'class',
};
// Material-UI configuration
export const muiThemeConfig = {
    defaultTheme: 'system',
    storageKey: 'mui-theme',
    enableSystem: true,
    disableTransitionOnChange: false,
    themes: ['light', 'dark'],
    attribute: 'data-theme',
    value: {
        light: 'light',
        dark: 'dark',
    },
};
// Chakra UI configuration
export const chakraThemeConfig = {
    defaultTheme: 'system',
    storageKey: 'chakra-theme',
    enableSystem: true,
    disableTransitionOnChange: false,
    themes: ['light', 'dark'],
    attribute: 'data-theme',
    value: {
        light: 'light',
        dark: 'dark',
    },
};
// Ant Design configuration
export const antdThemeConfig = {
    defaultTheme: 'system',
    storageKey: 'antd-theme',
    enableSystem: true,
    disableTransitionOnChange: false,
    themes: ['light', 'dark'],
    attribute: 'data-theme',
    value: {
        light: 'light',
        dark: 'dark',
    },
};
// Custom theme configuration builder
export function createThemeConfig(overrides = {}) {
    return {
        ...reactThemeConfig,
        ...overrides,
    };
}
// CSS variables for theme colors (can be used with any CSS framework)
export const themeVariables = {
    light: {
        '--background': '0 0% 100%',
        '--foreground': '222.2 84% 4.9%',
        '--card': '0 0% 100%',
        '--card-foreground': '222.2 84% 4.9%',
        '--popover': '0 0% 100%',
        '--popover-foreground': '222.2 84% 4.9%',
        '--primary': '222.2 47.4% 11.2%',
        '--primary-foreground': '210 40% 98%',
        '--secondary': '210 40% 96%',
        '--secondary-foreground': '222.2 84% 4.9%',
        '--muted': '210 40% 96%',
        '--muted-foreground': '215.4 16.3% 46.9%',
        '--accent': '210 40% 96%',
        '--accent-foreground': '222.2 84% 4.9%',
        '--destructive': '0 84.2% 60.2%',
        '--destructive-foreground': '210 40% 98%',
        '--border': '214.3 31.8% 91.4%',
        '--input': '214.3 31.8% 91.4%',
        '--ring': '222.2 84% 4.9%',
    },
    dark: {
        '--background': '222.2 84% 4.9%',
        '--foreground': '210 40% 98%',
        '--card': '222.2 84% 4.9%',
        '--card-foreground': '210 40% 98%',
        '--popover': '222.2 84% 4.9%',
        '--popover-foreground': '210 40% 98%',
        '--primary': '210 40% 98%',
        '--primary-foreground': '222.2 47.4% 11.2%',
        '--secondary': '217.2 32.6% 17.5%',
        '--secondary-foreground': '210 40% 98%',
        '--muted': '217.2 32.6% 17.5%',
        '--muted-foreground': '215 20.2% 65.1%',
        '--accent': '217.2 32.6% 17.5%',
        '--accent-foreground': '210 40% 98%',
        '--destructive': '0 62.8% 30.6%',
        '--destructive-foreground': '210 40% 98%',
        '--border': '217.2 32.6% 17.5%',
        '--input': '217.2 32.6% 17.5%',
        '--ring': '212.7 26.8% 83.9%',
    },
};
// Function to inject CSS variables
export function injectThemeVariables() {
    if (typeof document === 'undefined')
        return;
    const style = document.createElement('style');
    style.textContent = `
    :root {
      ${Object.entries(themeVariables.light)
        .map(([key, value]) => `${key}: ${value};`)
        .join('\n      ')}
    }
    
    .dark {
      ${Object.entries(themeVariables.dark)
        .map(([key, value]) => `${key}: ${value};`)
        .join('\n      ')}
    }
  `;
    document.head.appendChild(style);
}
// Theme detection utilities
export const themeUtils = {
    // Detect if user prefers dark mode
    prefersDark: () => {
        if (typeof window === 'undefined')
            return false;
        return window.matchMedia('(prefers-color-scheme: dark)').matches;
    },
    // Detect if user prefers reduced motion
    prefersReducedMotion: () => {
        if (typeof window === 'undefined')
            return false;
        return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    },
    // Get stored theme
    getStoredTheme: (storageKey = 'app-theme') => {
        if (typeof window === 'undefined')
            return null;
        try {
            return localStorage.getItem(storageKey);
        }
        catch {
            return null;
        }
    },
    // Set stored theme
    setStoredTheme: (theme, storageKey = 'app-theme') => {
        if (typeof window === 'undefined')
            return;
        try {
            localStorage.setItem(storageKey, theme);
        }
        catch {
            // Ignore localStorage errors
        }
    },
    // Remove stored theme
    removeStoredTheme: (storageKey = 'app-theme') => {
        if (typeof window === 'undefined')
            return;
        try {
            localStorage.removeItem(storageKey);
        }
        catch {
            // Ignore localStorage errors
        }
    },
};
export default {
    reactThemeConfig,
    nextThemeConfig,
    viteThemeConfig,
    tailwindThemeConfig,
    muiThemeConfig,
    chakraThemeConfig,
    antdThemeConfig,
    createThemeConfig,
    themeVariables,
    injectThemeVariables,
    themeUtils,
};
