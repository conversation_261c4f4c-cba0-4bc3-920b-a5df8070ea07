import React from "react";
import { type Role } from "../../hooks/useRoles";
import { type Permission } from "../../hooks/useMaster";
export interface RolesManagementProps {
    appName: string;
    adminApiUrl: string;
    orgApiUrl?: string;
    orgId?: string;
    setRoles?: (rolesList: Role[]) => void;
    setPermissions?: (permissionsList: Permission[]) => void;
}
declare const RolesManagement: React.FC<RolesManagementProps>;
export default RolesManagement;
