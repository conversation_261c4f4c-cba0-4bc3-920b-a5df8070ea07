import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "./ui/card";
import { Label } from "./ui/label";
import { Checkbox } from "./ui";
import { formatResourceName, Permission } from "../lib/permissionUtils";

interface PermissionSelectorProps {
  availablePermissions: Permission[];
  selectedPermissions: Permission[];
  onTogglePermission: (permissionId: string) => void;
}

const PermissionSelector: React.FC<PermissionSelectorProps> = ({
  availablePermissions,
  selectedPermissions,
  onTogglePermission,
}) => {
  const groupedPermissions = availablePermissions.reduce((acc, permission) => {
    if (!acc[permission.resource]) acc[permission.resource] = [];
    acc[permission.resource].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  return (
    <div
      style={{
        maxHeight: "300px",
        overflowY: "auto",
        paddingRight: "8px",
        display: "flex",
        flexDirection: "column",
        gap: "16px",
      }}>
      {Object.entries(groupedPermissions).map(([resource, perms]) => (
        <Card key={resource} className="border shadow-sm">
          <CardHeader className="pb-3 pt-4 px-5">
            <CardTitle className="text-base font-semibold">
              {formatResourceName(resource)}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 px-5 pb-4">
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                gap: "12px",
              }}>
              {perms.map((perm) => (
                <div
                  key={perm._id}
                  className="flex items-start space-x-3 py-2 px-2 rounded hover:bg-background-50"
                  style={{ minHeight: "40px" }}>
                  <Checkbox
                    id={perm._id}
                    checked={selectedPermissions.some(
                      (p) => p._id === perm._id
                    )}
                    onCheckedChange={() => onTogglePermission(perm._id)}
                    className="mt-1 flex-shrink-0"
                  />
                  <Label
                    htmlFor={perm._id}
                    className="ml-2 text-sm font-normal cursor-pointer leading-relaxed break-words"
                    style={{ wordBreak: "break-word" }}>
                    {perm.description}
                  </Label>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default PermissionSelector;
