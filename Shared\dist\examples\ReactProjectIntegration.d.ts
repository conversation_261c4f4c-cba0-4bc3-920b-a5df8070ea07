import React from 'react';
/**
 * This is how you set up the socket connection at the root of your React project
 */
export declare const App: React.FC;
/**
 * Example of a utility component that only needs logout functionality
 */
export declare const LogoutButton: React.FC<{
    className?: string;
}>;
/**
 * If you already have an AuthContext, you can integrate it like this
 */
interface AuthContextType {
    isAuthenticated: boolean;
    accessToken: string | null;
    logout: () => Promise<void>;
}
export declare const AppWithAuthContext: React.FC<{
    auth: AuthContextType;
}>;
export {};
