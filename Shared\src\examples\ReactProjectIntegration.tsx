import React, { useEffect, useState } from 'react';
import { SocketProvider, useSocketContext, useSocketLogout } from '../context/SocketContext';

// ===== 1. MAIN APP SETUP (Root Level) =====
/**
 * This is how you set up the socket connection at the root of your React project
 */
export const App: React.FC = () => {
  // Your authentication state (from Redux, Context, etc.)
  const [isAuthenticated, setIsAuthenticated] = useState(true);
  const [accessToken, setAccessToken] = useState("your-jwt-token");
  
  // Socket server URL - pass this from your environment or config
  const socketUrl = process.env.REACT_APP_SOCKET_URL || "ws://localhost:8081";

  // Your existing logout function
  const handleAuthLogout = async () => {
    console.log("🔐 Performing authentication logout...");
    
    // Clear authentication state
    setIsAuthenticated(false);
    setAccessToken("");
    
    // Clear local storage
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    
    // Redirect to login or update app state
    window.location.href = '/login';
    // OR if using React Router: navigate('/login');
  };

  return (
    <SocketProvider
      socketUrl={socketUrl}
      isAuthenticated={isAuthenticated}
      accessToken={accessToken}
      onAuthLogout={handleAuthLogout}
    >
      {/* Your app components */}
      <Header />
      <MainContent />
      <Footer />
    </SocketProvider>
  );
};

// ===== 2. HEADER COMPONENT (Individual Logout) =====
/**
 * Example of calling logout from individual component
 */
const Header: React.FC = () => {
  // Get logout function from socket context
  const logout = useSocketLogout();
  const { isConnected } = useSocketContext();

  const handleLogout = async () => {
    try {
      console.log("🚪 Logging out from header...");
      await logout(); // This handles both socket and auth logout
      console.log("✅ Successfully logged out");
    } catch (error) {
      console.error("❌ Logout failed:", error);
      // Show error message to user
      alert("Logout failed. Please try again.");
    }
  };

  return (
    <header style={{ padding: '1rem', borderBottom: '1px solid #ccc' }}>
      <h1>My App</h1>
      <div>
        <span>Socket: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}</span>
        <button 
          onClick={handleLogout}
          style={{ marginLeft: '1rem', padding: '0.5rem 1rem' }}
        >
          Logout
        </button>
      </div>
    </header>
  );
};

// ===== 3. MAIN CONTENT (Socket Events) =====
/**
 * Example of using socket events in a component
 */
const MainContent: React.FC = () => {
  const { emit, on, off, isConnected } = useSocketContext();
  const [messages, setMessages] = useState<string[]>([]);

  useEffect(() => {
    if (isConnected) {
      // Listen for messages
      const handleMessage = (data: any) => {
        console.log("📨 Message received:", data);
        setMessages(prev => [...prev, data.text]);
      };

      const handleNotification = (data: any) => {
        console.log("🔔 Notification:", data);
        // Show notification to user
      };

      // Set up event listeners
      on('message', handleMessage);
      on('notification', handleNotification);

      // Cleanup on unmount or disconnect
      return () => {
        off('message', handleMessage);
        off('notification', handleNotification);
      };
    }
  }, [isConnected, on, off]);

  const sendMessage = () => {
    if (isConnected) {
      const message = `Hello from client at ${new Date().toLocaleTimeString()}`;
      emit('sendMessage', { text: message, timestamp: Date.now() });
    }
  };

  return (
    <main style={{ padding: '2rem' }}>
      <h2>Main Content</h2>
      
      <div style={{ marginBottom: '1rem' }}>
        <button 
          onClick={sendMessage} 
          disabled={!isConnected}
          style={{ padding: '0.5rem 1rem' }}
        >
          Send Message
        </button>
      </div>

      <div>
        <h3>Messages:</h3>
        <ul>
          {messages.map((msg, index) => (
            <li key={index}>{msg}</li>
          ))}
        </ul>
      </div>
    </main>
  );
};

// ===== 4. FOOTER COMPONENT (Another Logout Example) =====
/**
 * Another component that can independently call logout
 */
const Footer: React.FC = () => {
  const logout = useSocketLogout();
  const { isConnected, connectionError } = useSocketContext();

  const handleQuickLogout = async () => {
    const confirmed = window.confirm("Are you sure you want to logout?");
    if (confirmed) {
      try {
        await logout();
      } catch (error) {
        console.error("Quick logout failed:", error);
      }
    }
  };

  return (
    <footer style={{ padding: '1rem', borderTop: '1px solid #ccc', marginTop: '2rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <p>Socket Status: {isConnected ? 'Connected' : 'Disconnected'}</p>
          {connectionError && (
            <p style={{ color: 'red', fontSize: '0.8rem' }}>
              Error: {connectionError}
            </p>
          )}
        </div>
        
        <button 
          onClick={handleQuickLogout}
          style={{ padding: '0.5rem 1rem', backgroundColor: '#ff4444', color: 'white' }}
        >
          Quick Logout
        </button>
      </div>
    </footer>
  );
};

// ===== 5. UTILITY COMPONENT (Custom Hook Usage) =====
/**
 * Example of a utility component that only needs logout functionality
 */
export const LogoutButton: React.FC<{ className?: string }> = ({ className }) => {
  const logout = useSocketLogout();

  const handleClick = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout button failed:", error);
    }
  };

  return (
    <button onClick={handleClick} className={className}>
      Logout
    </button>
  );
};

// ===== 6. INTEGRATION WITH EXISTING AUTH CONTEXT =====
/**
 * If you already have an AuthContext, you can integrate it like this
 */
interface AuthContextType {
  isAuthenticated: boolean;
  accessToken: string | null;
  logout: () => Promise<void>;
}

export const AppWithAuthContext: React.FC<{ auth: AuthContextType }> = ({ auth }) => {
  const socketUrl = "ws://localhost:8081";

  return (
    <SocketProvider
      socketUrl={socketUrl}
      isAuthenticated={auth.isAuthenticated}
      accessToken={auth.accessToken || undefined}
      onAuthLogout={auth.logout}
    >
      <YourAppComponents />
    </SocketProvider>
  );
};

// Placeholder component
const YourAppComponents: React.FC = () => <div>Your app components here</div>;
