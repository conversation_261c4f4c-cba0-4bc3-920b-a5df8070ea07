import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { LogOut, User } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, } from "../ui/select";
import { Avatar, AvatarFallback } from "../ui/avatar";
import { useTranslation } from "react-i18next";
import { useDecodedJwt } from "../../hooks/useDecodedJwt";
export function UserProfile({ onLogout, }) {
    const { t } = useTranslation();
    const jwtPayload = useDecodedJwt();
    const firstName = jwtPayload?.firstName || "";
    const lastName = jwtPayload?.lastName || "";
    const userName = `${firstName} ${lastName}`.trim() || "";
    const userInitials = `${firstName?.[0] || ""}${lastName?.[0] || ""}`.toUpperCase() || "U";
    const handleLogout = () => {
        if (onLogout)
            onLogout();
        // else logout(); // Uncomment if using useAuth
    };
    const handleAction = (action) => {
        switch (action) {
            case "profile":
                console.log("Profile clicked");
                break;
            case "logout":
                handleLogout();
                break;
            default:
                break;
        }
    };
    return (_jsxs("div", { className: "flex items-center gap-3", children: [_jsx("div", { className: "hidden md:block text-sm", children: _jsx("div", { className: "font-medium", children: userName }) }), _jsxs(Select, { onValueChange: handleAction, children: [_jsx(SelectTrigger, { style: { border: "none", boxShadow: "none", outline: "none", width: '20px', marginRight: "5px" }, className: "focus:ring-0 focus:outline-none ring-0 outline-none", children: _jsx(Avatar, { style: { border: "none" }, children: _jsx(AvatarFallback, { children: userInitials }) }) }), _jsxs(SelectContent, { className: "border-0 shadow-none", children: [_jsx(SelectItem, { value: "profile", children: _jsxs("div", { className: "flex items-center gap-2", children: [_jsx(User, { className: "h-4 w-4" }), _jsx("span", { children: t("Profile") })] }) }), _jsx(SelectItem, { value: "logout", children: _jsxs("div", { className: "flex items-center gap-2", children: [_jsx(LogOut, { className: "h-4 w-4" }), _jsx("span", { children: t("Logout") })] }) })] })] })] }));
}
export default UserProfile;
