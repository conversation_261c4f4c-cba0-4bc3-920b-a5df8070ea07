import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from "react";
import { Button } from "./button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "./dialog";
import { Input } from "./input";
import { Label } from "./label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./select";
/**
 * Example component demonstrating the usage of various UI components
 * This is for demonstration purposes and can be removed in production
 */
export function ExampleForm() {
    const [name, setName] = React.useState("");
    const [email, setEmail] = React.useState("");
    const [role, setRole] = React.useState("");
    return (_jsxs("div", { className: "space-y-4 p-4", children: [_jsx("h2", { className: "text-2xl font-bold", children: "Example Form" }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "name", children: "Name" }), _jsx(Input, { id: "name", placeholder: "Enter your name", value: name, onChange: (e) => setName(e.target.value) })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "email", children: "Email" }), _jsx(Input, { id: "email", type: "email", placeholder: "Enter your email", value: email, onChange: (e) => setEmail(e.target.value) })] }), _jsxs("div", { className: "space-y-2", children: [_jsx(Label, { htmlFor: "role", children: "Role" }), _jsxs(Select, { value: role, onValueChange: setRole, children: [_jsx(SelectTrigger, { children: _jsx(SelectValue, { placeholder: "Select a role" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "admin", children: "Admin" }), _jsx(SelectItem, { value: "user", children: "User" }), _jsx(SelectItem, { value: "guest", children: "Guest" })] })] })] }), _jsxs("div", { className: "flex space-x-2", children: [_jsx(Button, { type: "submit", children: "Submit" }), _jsx(Button, { variant: "outline", children: "Cancel" }), _jsxs(Dialog, { children: [_jsx(DialogTrigger, { asChild: true, children: _jsx(Button, { variant: "secondary", children: "Open Dialog" }) }), _jsxs(DialogContent, { children: [_jsxs(DialogHeader, { children: [_jsx(DialogTitle, { children: "Confirm Action" }), _jsx(DialogDescription, { children: "Are you sure you want to submit this form?" })] }), _jsxs(DialogFooter, { children: [_jsx(Button, { variant: "outline", children: "Cancel" }), _jsx(Button, { children: "Confirm" })] })] })] })] })] }));
}
export default ExampleForm;
