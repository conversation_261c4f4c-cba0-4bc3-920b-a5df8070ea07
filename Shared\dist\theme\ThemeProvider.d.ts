export type Theme = 'dark' | 'light' | 'system';
export interface ThemeProviderProps {
    children: React.ReactNode;
    defaultTheme?: Theme;
    storageKey?: string;
}
export interface ThemeProviderState {
    theme: Theme;
    setTheme: (theme: Theme) => void;
}
export declare function ThemeProvider({ children, defaultTheme, storageKey, ...props }: ThemeProviderProps): import("react/jsx-runtime").JSX.Element;
export declare const useTheme: () => ThemeProviderState;
export declare const getSystemTheme: () => "light" | "dark";
export declare const applyTheme: (theme: Theme, storageKey?: string) => void;
