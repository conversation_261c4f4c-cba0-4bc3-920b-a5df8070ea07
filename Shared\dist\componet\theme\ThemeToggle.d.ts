interface ThemeToggleProps {
    variant?: 'select' | 'buttons' | 'cards' | 'icon';
    size?: 'sm' | 'default' | 'lg';
    showLabels?: boolean;
    showDescriptions?: boolean;
    className?: string;
}
export declare function ThemeToggle({ variant, size, showLabels, showDescriptions, className, }: ThemeToggleProps): import("react/jsx-runtime").JSX.Element;
export declare function ThemeToggleButton(): import("react/jsx-runtime").JSX.Element;
export declare function ThemeSelector(): import("react/jsx-runtime").JSX.Element;
export declare function ThemeSwitcher(): import("react/jsx-runtime").JSX.Element;
export default ThemeToggle;
