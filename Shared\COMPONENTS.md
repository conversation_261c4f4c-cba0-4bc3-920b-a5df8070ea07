# UI Components Documentation

This document describes the UI components that have been implemented in this shared library.

## Available Components

### 1. Button Component
A versatile button component built with Radix UI's Slot primitive.

**Features:**
- Multiple variants: `default`, `destructive`, `outline`, `secondary`, `ghost`, `link`
- Multiple sizes: `default`, `sm`, `lg`, `icon`
- Support for `asChild` prop for composition
- Full TypeScript support

**Usage:**
```tsx
import { Button } from '@shared/cloud_rps';

<Button variant="default" size="lg">Click me</Button>
<Button variant="outline">Cancel</Button>
<Button asChild>
  <a href="/link">Link Button</a>
</Button>
```

### 2. Dialog Component
A modal dialog component built with Radix UI Dialog primitive.

**Components:**
- `Dialog` - Root component
- `DialogTrigger` - Trigger element
- `DialogContent` - Main content container
- `DialogHeader` - Header section
- `DialogFooter` - Footer section
- `DialogTitle` - Title element
- `DialogDescription` - Description element
- `DialogClose` - Close button

**Usage:**
```tsx
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@shared/cloud_rps';

<Dialog>
  <DialogTrigger asChild>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
      <DialogDescription>Dialog description goes here.</DialogDescription>
    </DialogHeader>
    <DialogFooter>
      <Button>Confirm</Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### 3. Select Component
A select dropdown component built with Radix UI Select primitive.

**Components:**
- `Select` - Root component
- `SelectTrigger` - Trigger button
- `SelectValue` - Selected value display
- `SelectContent` - Dropdown content
- `SelectItem` - Individual option
- `SelectLabel` - Group label
- `SelectSeparator` - Visual separator

**Usage:**
```tsx
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@shared/cloud_rps';

<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select an option" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>
```

### 4. Input Component
A styled input component with consistent theming.

**Usage:**
```tsx
import { Input } from '@shared/cloud_rps';

<Input placeholder="Enter text" />
<Input type="email" placeholder="Enter email" />
```

### 5. Label Component
A label component built with Radix UI Label primitive.

**Usage:**
```tsx
import { Label } from '@shared/cloud_rps';

<Label htmlFor="input-id">Label Text</Label>
```

## Utility Functions

### cn() Function
A utility function for merging CSS classes using `clsx`.

**Usage:**
```tsx
import { cn } from '@shared/cloud_rps';

const className = cn(
  "base-class",
  condition && "conditional-class",
  { "object-class": someCondition }
);
```

## Theme Integration

All components are designed to work with the existing theme system:

```tsx
import { ThemeProvider } from '@shared/cloud_rps';

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="app-theme">
      {/* Your components here */}
    </ThemeProvider>
  );
}
```

## Example Usage

See `src/componet/ui/example.tsx` for a complete example demonstrating how to use multiple components together.

## Styling

Components use CSS classes that should be styled with your CSS framework (e.g., Tailwind CSS). The classes follow common naming conventions:

- `bg-primary`, `text-primary-foreground` - Primary colors
- `bg-secondary`, `text-secondary-foreground` - Secondary colors
- `border-input`, `bg-background` - Input styling
- `hover:`, `focus:`, `disabled:` - State modifiers

## Dependencies

The components rely on the following Radix UI primitives:
- `@radix-ui/react-slot`
- `@radix-ui/react-dialog`
- `@radix-ui/react-select`
- `@radix-ui/react-label`

And utility libraries:
- `class-variance-authority` - For component variants
- `clsx` - For class name merging
