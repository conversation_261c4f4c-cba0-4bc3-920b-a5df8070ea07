import { ReactNode } from 'react';
export type Theme = 'dark' | 'light' | 'system';
export interface ThemeConfig {
    defaultTheme?: Theme;
    storageKey?: string;
    enableSystem?: boolean;
    disableTransitionOnChange?: boolean;
    themes?: string[];
    attribute?: 'class' | 'data-theme';
    value?: Record<string, string>;
}
export interface ThemeProviderState {
    theme: Theme;
    setTheme: (theme: Theme) => void;
    themes: string[];
    systemTheme: 'light' | 'dark';
    resolvedTheme: 'light' | 'dark';
}
interface ThemeProviderProps extends ThemeConfig {
    children: ReactNode;
}
export declare function UniversalThemeProvider({ children, defaultTheme, storageKey, enableSystem, disableTransitionOnChange, themes, attribute, value, }: ThemeProviderProps): import("react/jsx-runtime").JSX.Element;
export declare const useUniversalTheme: () => ThemeProviderState;
export declare const getSystemTheme: () => "light" | "dark";
export declare const applyTheme: (theme: Theme, config?: Partial<ThemeConfig>) => void;
export declare const useThemeAware: () => {
    theme: Theme;
    resolvedTheme: "dark" | "light";
    setTheme: (theme: Theme) => void;
    isDark: boolean;
    isLight: boolean;
    isSystem: boolean;
    toggleTheme: () => void;
    cycleTheme: () => void;
};
export default UniversalThemeProvider;
