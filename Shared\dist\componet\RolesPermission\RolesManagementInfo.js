import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from "react";
import { Plus, Edit2, Trash2, Shield, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import AddEditRoleModal from "./AddEditRoleModal";
import ConfirmDialog from "../confirmation/ConfirmDialog";
const RolesManagementInfo = ({ roles, permissions, isLoading = false, isMutating = false, onCreateRole, onUpdateRole, onDeleteRole, }) => {
    const [dialogOpen, setDialogOpen] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [currentRole, setCurrentRole] = useState({
        name: "",
        description: "",
        permissionIds: [],
    });
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [roleToDelete, setRoleToDelete] = useState(null);
    const openCreateDialog = () => {
        setCurrentRole({
            name: "",
            description: "",
            permissionIds: [],
        });
        setIsEdit(false);
        setDialogOpen(true);
    };
    const openEditDialog = (role) => {
        setCurrentRole({
            id: role.id || role._id,
            name: role.name,
            description: role.description,
            permissionIds: Array.isArray(role.permissionIds)
                ? role.permissionIds
                : [],
        });
        setIsEdit(true);
        setDialogOpen(true);
    };
    const handleSave = () => {
        if (isEdit && currentRole.id) {
            onUpdateRole({
                id: currentRole.id,
                name: currentRole.name || "",
                description: currentRole.description || "",
                permissionIds: currentRole.permissionIds || [],
            });
        }
        else {
            onCreateRole({
                name: currentRole.name || "",
                description: currentRole.description || "",
                permissionIds: currentRole.permissionIds || [],
            });
        }
        setDialogOpen(false);
    };
    const handleDelete = (id) => {
        setRoleToDelete(id);
        setConfirmOpen(true);
    };
    const handleConfirmDelete = () => {
        if (roleToDelete) {
            onDeleteRole(roleToDelete);
            setConfirmOpen(false);
            setRoleToDelete(null);
        }
    };
    const getPermissionName = (id) => {
        const perm = permissions.find((p) => (p.id || p._id) === id);
        if (!perm)
            return id;
        return perm.description || `${perm.resource}:${perm.action}`;
    };
    return (_jsxs("div", { className: "max-w-7xl mx-auto p-6", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsxs("div", { className: "flex justify-between items-center", children: [_jsx(CardTitle, { className: "text-2xl font-bold", children: "Roles & Permissions" }), _jsxs(Button, { onClick: openCreateDialog, disabled: isMutating, className: "flex items-center gap-2", children: [_jsx(Plus, { className: "w-4 h-4" }), "Create Role"] })] }) }), _jsx(CardContent, { children: isLoading || isMutating ? (_jsxs("div", { className: "flex justify-center items-center min-h-[200px]", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin mr-2" }), _jsx("span", { children: "Loading..." })] })) : (_jsx("div", { className: "grid grid-cols-1 gap-4", children: roles.map((role) => (_jsx(Card, { className: "border", children: _jsx(CardContent, { className: "p-4", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex gap-3 items-center", children: [_jsx("div", { className: "bg-blue-100 p-2 rounded-lg", children: _jsx(Shield, { className: "w-5 h-5 text-blue-600" }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-semibold text-lg", children: role.name }), _jsx("p", { className: "text-sm text-muted-foreground mb-2", children: role.description }), _jsx("div", { className: "flex flex-wrap gap-1 mt-2", children: (role.permissionIds || []).map((pid) => (_jsx(Badge, { variant: "secondary", className: "text-xs", children: getPermissionName(pid) }, pid))) })] })] }), _jsxs("div", { className: "flex gap-2", children: [_jsx(Button, { variant: "outline", size: "sm", onClick: () => openEditDialog(role), disabled: isMutating, children: _jsx(Edit2, { className: "w-4 h-4" }) }), _jsx(Button, { variant: "outline", size: "sm", onClick: () => handleDelete(role.id || role._id || ""), disabled: isMutating, className: "text-destructive hover:text-destructive-foreground", children: _jsx(Trash2, { className: "w-4 h-4" }) })] })] }) }) }, role.id || role._id))) })) })] }), _jsx(AddEditRoleModal, { open: dialogOpen, onClose: () => setDialogOpen(false), role: currentRole, setRole: setCurrentRole, isEdit: isEdit, onSave: handleSave, availablePermissions: permissions.map((perm) => ({
                    id: perm.id || perm._id || "",
                    name: perm.description || `${perm.resource}:${perm.action}`,
                    category: perm.resource,
                })) }), _jsx(ConfirmDialog, { open: confirmOpen, onCancel: () => setConfirmOpen(false), onConfirm: handleConfirmDelete, title: "Delete Role", description: "Are you sure you want to delete this role? This action cannot be undone.", confirmText: "Yes, Delete", cancelText: "Cancel" })] }));
};
export default RolesManagementInfo;
