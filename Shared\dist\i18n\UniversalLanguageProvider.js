import { jsx as _jsx } from "react/jsx-runtime";
import { createContext, useContext, useEffect, useState } from 'react';
// Default language configurations
export const defaultLanguages = [
    { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
    { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' },
    { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' },
    { code: 'it', name: 'Italian', nativeName: 'Italiano', flag: '🇮🇹' },
    { code: 'pt', name: 'Portuguese', nativeName: 'Português', flag: '🇵🇹' },
    { code: 'ru', name: 'Russian', nativeName: 'Русский', flag: '🇷🇺' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵' },
    { code: 'ko', name: 'Korean', nativeName: '한국어', flag: '🇰🇷' },
    { code: 'zh', name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦', rtl: true },
    { code: 'he', name: 'Hebrew', nativeName: 'עברית', flag: '🇮🇱', rtl: true },
];
const defaultConfig = {
    defaultLanguage: 'en',
    storageKey: 'app-language',
    languages: defaultLanguages,
    fallbackLanguage: 'en',
    detectBrowserLanguage: true,
};
const LanguageProviderContext = createContext(undefined);
// Detect browser language
function getBrowserLanguage(availableLanguages) {
    if (typeof window === 'undefined')
        return 'en';
    const browserLanguages = navigator.languages || [navigator.language];
    for (const browserLang of browserLanguages) {
        // Check exact match
        const exactMatch = availableLanguages.find(lang => lang.code === browserLang);
        if (exactMatch)
            return exactMatch.code;
        // Check language prefix (e.g., 'en-US' -> 'en')
        const langPrefix = browserLang.split('-')[0];
        const prefixMatch = availableLanguages.find(lang => lang.code === langPrefix);
        if (prefixMatch)
            return prefixMatch.code;
    }
    return 'en';
}
// Simple translation function
function createTranslationFunction(translations, language, fallbackLanguage) {
    return (key, params) => {
        const languageTranslations = translations[language] || {};
        const fallbackTranslations = translations[fallbackLanguage] || {};
        let translation = languageTranslations[key] || fallbackTranslations[key] || key;
        // Simple parameter replacement
        if (params) {
            Object.entries(params).forEach(([paramKey, value]) => {
                translation = translation.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(value));
            });
        }
        return translation;
    };
}
export function LanguageProvider({ children, defaultLanguage = defaultConfig.defaultLanguage, storageKey = defaultConfig.storageKey, languages = defaultConfig.languages, fallbackLanguage = defaultConfig.fallbackLanguage, detectBrowserLanguage = defaultConfig.detectBrowserLanguage, translations = {}, onLanguageChange, }) {
    const [language, setLanguageState] = useState(() => {
        if (typeof window === 'undefined')
            return defaultLanguage;
        try {
            // Check stored language first
            const stored = localStorage.getItem(storageKey);
            if (stored && languages.some(lang => lang.code === stored)) {
                return stored;
            }
            // Detect browser language if enabled
            if (detectBrowserLanguage) {
                return getBrowserLanguage(languages);
            }
            return defaultLanguage;
        }
        catch {
            return defaultLanguage;
        }
    });
    const currentLanguage = languages.find(lang => lang.code === language);
    const isRTL = currentLanguage?.rtl || false;
    // Apply RTL/LTR direction to document
    useEffect(() => {
        if (typeof document !== 'undefined') {
            document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
            document.documentElement.lang = language;
        }
    }, [language, isRTL]);
    const setLanguage = (newLanguage) => {
        if (!languages.some(lang => lang.code === newLanguage)) {
            console.warn(`Language "${newLanguage}" is not available`);
            return;
        }
        try {
            localStorage.setItem(storageKey, newLanguage);
        }
        catch {
            // Ignore localStorage errors
        }
        setLanguageState(newLanguage);
        onLanguageChange?.(newLanguage);
    };
    const t = createTranslationFunction(translations, language, fallbackLanguage);
    const contextValue = {
        language,
        setLanguage,
        languages,
        currentLanguage,
        isRTL,
        t,
    };
    return (_jsx(LanguageProviderContext.Provider, { value: contextValue, children: children }));
}
export const useLanguage = () => {
    const context = useContext(LanguageProviderContext);
    if (context === undefined) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
};
// Utility functions
export const languageUtils = {
    // Get stored language
    getStoredLanguage: (storageKey = 'app-language') => {
        if (typeof window === 'undefined')
            return null;
        try {
            return localStorage.getItem(storageKey);
        }
        catch {
            return null;
        }
    },
    // Set stored language
    setStoredLanguage: (language, storageKey = 'app-language') => {
        if (typeof window === 'undefined')
            return;
        try {
            localStorage.setItem(storageKey, language);
        }
        catch {
            // Ignore localStorage errors
        }
    },
    // Detect browser language
    detectBrowserLanguage: (availableLanguages) => {
        return getBrowserLanguage(availableLanguages);
    },
    // Get language by code
    getLanguageByCode: (code, languages) => {
        return languages.find(lang => lang.code === code);
    },
    // Check if language is RTL
    isRTLLanguage: (code, languages) => {
        const language = languages.find(lang => lang.code === code);
        return language?.rtl || false;
    },
};
export default LanguageProvider;
