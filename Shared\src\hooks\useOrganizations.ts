// Placeholder hook for organizations
// This should be implemented based on your actual API and data fetching logic

import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/api';

export interface Organization {
  id?: string;
  _id?: string;
  organizationName: string;
  selectedAppLicenses?: any[];
}

// API functions
const organizationsApi = {
  getAll: async (): Promise<Organization[]> => {
    try {
      const response = await api.get('/api/organizations');
      return response.data?.data || [];
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error("You do not have permission to access organizations");
      }
      throw new Error("Failed to fetch organizations");
    }
  },
};

export const useOrganizations = () => {
  return useQuery({
    queryKey: ['organizations'],
    queryFn: organizationsApi.getAll,
  });
};