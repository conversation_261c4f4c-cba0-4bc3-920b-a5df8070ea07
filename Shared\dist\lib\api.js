// Shared API configuration using axios
import axios from 'axios';
// Default API URL (fallback)
const DEFAULT_API_URL = "https://cloud-api-admin-dev.rpsmobile.com";
// Global variable to store API URL passed from parent app
let externalApiUrl = null;
// Function to set API URL from parent app
export const setApiUrl = (apiUrl) => {
    externalApiUrl = apiUrl;
    // Update the axios instance with new base URL
    apiClient.defaults.baseURL = apiUrl;
};
// Get the API base URL
const getApiBaseUrl = () => {
    // Use external API URL if provided, otherwise use default
    return externalApiUrl || DEFAULT_API_URL;
};
const API_BASE_URL = getApiBaseUrl();
// Export configuration for debugging/testing
export const apiConfig = {
    currentUrl: API_BASE_URL,
    apiUrl: externalApiUrl || DEFAULT_API_URL,
    environment: typeof window !== 'undefined' ? window.location.hostname : 'server'
};
// Create main axios instance
export const apiClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
});
// Request interceptor to add auth token from localStorage
apiClient.interceptors.request.use((config) => {
    const token = localStorage.getItem('access_token');
    if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error) => {
    return Promise.reject(error);
});
// Response interceptor for error handling
apiClient.interceptors.response.use((response) => {
    return response;
}, (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
        // Token expired or invalid
        localStorage.removeItem('access_token');
        console.error('Authentication failed:', error.response.data?.message);
    }
    else if (error.response?.status === 403) {
        // Forbidden - user doesn't have permission
        console.error('Access forbidden:', error.response.data?.message);
    }
    else if (error.response?.status >= 500) {
        // Server error
        console.error('Server error:', error.response.data?.message);
    }
    return Promise.reject(error);
});
// Helper functions for common API operations
export const api = {
    // GET request
    get: (url, config) => {
        return apiClient.get(url, config);
    },
    // POST request
    post: (url, data, config) => {
        return apiClient.post(url, data, config);
    },
    // PUT request
    put: (url, data, config) => {
        return apiClient.put(url, data, config);
    },
    // PATCH request
    patch: (url, data, config) => {
        return apiClient.patch(url, data, config);
    },
    // DELETE request
    delete: (url, config) => {
        return apiClient.delete(url, config);
    },
};
// Export the axios instance for direct use if needed
export default apiClient;
