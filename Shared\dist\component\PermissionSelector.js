import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Label } from "./ui/label";
import { Checkbox } from "./ui";
import { formatResourceName } from "../lib/permissionUtils";
const PermissionSelector = ({ availablePermissions, selectedPermissions, onTogglePermission, }) => {
    const groupedPermissions = availablePermissions.reduce((acc, permission) => {
        if (!acc[permission.resource])
            acc[permission.resource] = [];
        acc[permission.resource].push(permission);
        return acc;
    }, {});
    return (_jsx("div", { style: {
            maxHeight: "300px",
            overflowY: "auto",
            paddingRight: "8px",
            display: "flex",
            flexDirection: "column",
            gap: "16px",
        }, children: Object.entries(groupedPermissions).map(([resource, perms]) => (_jsxs(Card, { className: "border shadow-sm", children: [_jsx(CardHeader, { className: "pb-3 pt-4 px-5", children: _jsx(CardTitle, { className: "text-base font-semibold", children: formatResourceName(resource) }) }), _jsx(CardContent, { className: "pt-0 px-5 pb-4", children: _jsx("div", { style: {
                            display: "grid",
                            gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                            gap: "12px",
                        }, children: perms.map((perm) => (_jsxs("div", { className: "flex items-start space-x-3 py-2 px-2 rounded hover:bg-background-50", style: { minHeight: "40px" }, children: [_jsx(Checkbox, { id: perm._id, checked: selectedPermissions.some((p) => p._id === perm._id), onCheckedChange: () => onTogglePermission(perm._id), className: "mt-1 flex-shrink-0" }), _jsx(Label, { htmlFor: perm._id, className: "ml-2 text-sm font-normal cursor-pointer leading-relaxed break-words", style: { wordBreak: "break-word" }, children: perm.description })] }, perm._id))) }) })] }, resource))) }));
};
export default PermissionSelector;
