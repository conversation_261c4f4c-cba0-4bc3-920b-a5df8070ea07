import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Moon, Sun, Monitor } from "lucide-react";
import { useTheme } from "next-themes";
import { But<PERSON> } from "../ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from "../ui/select";
export function ThemeToggle() {
    const { setTheme, theme } = useTheme();
    const getCurrentIcon = () => {
        switch (theme) {
            case "light":
                return _jsx(Sun, { className: "h-4 w-4" });
            case "dark":
                return _jsx(Moon, { className: "h-4 w-4" });
            case "system":
                return _jsx(Monitor, { className: "h-4 w-4" });
            default:
                return _jsx(Monitor, { className: "h-4 w-4" });
        }
    };
    return (_jsxs(Select, { value: theme, onValueChange: setTheme, children: [_jsx(SelectTrigger, { className: "w-40", children: _jsxs("div", { className: "flex items-center gap-2", children: [getCurrentIcon(), _jsx(SelectValue, { placeholder: "Select theme" })] }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "light", children: _jsx("div", { className: "flex items-center gap-2", children: "Light" }) }), _jsx(SelectItem, { value: "dark", children: _jsx("div", { className: "flex items-center gap-2", children: "Dark" }) }), _jsx(SelectItem, { value: "system", children: _jsx("div", { className: "flex items-center gap-2", children: "System" }) })] })] }));
}
// Icon-only toggle button
export function ThemeToggleButton() {
    const { setTheme, theme } = useTheme();
    const cycleTheme = () => {
        const themes = ["light", "dark", "system"];
        const currentIndex = themes.indexOf(theme || "system");
        const nextIndex = (currentIndex + 1) % themes.length;
        setTheme(themes[nextIndex]);
    };
    const getCurrentIcon = () => {
        switch (theme) {
            case "light":
                return _jsx(Sun, { className: "h-4 w-4" });
            case "dark":
                return _jsx(Moon, { className: "h-4 w-4" });
            case "system":
                return _jsx(Monitor, { className: "h-4 w-4" });
            default:
                return _jsx(Monitor, { className: "h-4 w-4" });
        }
    };
    return (_jsx(Button, { variant: "outline", size: "icon", onClick: cycleTheme, title: `Current theme: ${theme || 'system'} - Click to cycle`, children: getCurrentIcon() }));
}
// Default export
export default ThemeToggle;
