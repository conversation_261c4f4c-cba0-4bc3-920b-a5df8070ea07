import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React, { useState } from "react";
import { Plus, Trash2, Shield, Loader2, EditIcon } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import AddEditRoleModal from "./AddEditRoleModal";
import ConfirmDialog from "../confirmation/ConfirmDialog";
import { useRoles as useRolesManagement, useCreateRole, useUpdateRole, useDeleteRole, } from "../../hooks/useRoles";
import { useOrgPermissions, usePermissions as useRolePermissions, } from "../../hooks/useMaster";
import { ApplicationName } from "../../lib/Schemas/Personnel";
import { setApiUrl } from "../../lib/api";
import { useOrgRoles, useCreateOrgRole, useUpdateOrgRole, useDeleteOrgRole, } from "../../hooks/useOrgRoles";
import { useLanguage } from "../../i18n/LanguageProvider";
const RolesManagement = ({ appName, adminApiUrl, orgApiUrl, orgId, setRoles, setPermissions, }) => {
    const { t } = useLanguage();
    // Set API URL based on appName and props
    React.useEffect(() => {
        const apiUrl = appName === ApplicationName.ORGANIZATION_ADMIN && orgApiUrl
            ? orgApiUrl
            : adminApiUrl;
        // You can set the API URL globally here if needed
        setApiUrl(apiUrl); // Uncomment if you want to use global API URL setting
    }, [appName, adminApiUrl, orgApiUrl, orgId]);
    // API hooks
    const { data: rolesData, isLoading: rolesLoading, error: rolesError, } = appName === ApplicationName.ORGANIZATION_ADMIN
        ? useOrgRoles(orgId)
        : useRolesManagement(appName);
    const { data: permissions = [], isLoading: permissionsLoading } = appName === ApplicationName.ORGANIZATION_ADMIN
        ? useOrgPermissions(adminApiUrl)
        : useRolePermissions(adminApiUrl);
    // Extract roles from the response data
    const roles = rolesData?.data || [];
    if (rolesData && setRoles) {
        setRoles(roles);
    }
    if (setPermissions && permissions?.length > 0) {
        setPermissions(permissions);
    }
    // Mutation hooks - use different hooks based on appName
    const createRoleMutation = appName === ApplicationName.ORGANIZATION_ADMIN
        ? useCreateOrgRole(orgId)
        : useCreateRole();
    const updateRoleMutation = appName === ApplicationName.ORGANIZATION_ADMIN
        ? useUpdateOrgRole(orgId)
        : useUpdateRole();
    const deleteRoleMutation = appName === ApplicationName.ORGANIZATION_ADMIN
        ? useDeleteOrgRole(orgId)
        : useDeleteRole();
    // Loading and mutation states
    const isLoading = rolesLoading || permissionsLoading;
    const isMutating = createRoleMutation.isPending ||
        updateRoleMutation.isPending ||
        deleteRoleMutation.isPending;
    // Local state
    const [dialogOpen, setDialogOpen] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [currentRole, setCurrentRole] = useState({
        name: "",
        description: "",
        permissionsList: [],
    });
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [roleToDelete, setRoleToDelete] = useState(null);
    // Event handlers
    const openCreateDialog = () => {
        setCurrentRole({
            name: "",
            description: "",
            permissionsList: [],
        });
        setIsEdit(false);
        setDialogOpen(true);
    };
    const openEditDialog = (role) => {
        setCurrentRole({
            _id: role._id,
            name: role.name,
            description: role.description,
            permissionsList: Array.isArray(role.permissionsList)
                ? role.permissionsList
                : [],
        });
        setIsEdit(true);
        setDialogOpen(true);
    };
    const handleSave = () => {
        if (isEdit && currentRole._id) {
            const updateData = {
                id: currentRole._id,
                name: currentRole.name || "",
                description: currentRole.description || "",
                permissionsList: currentRole.permissionsList || [],
            };
            updateRoleMutation.mutate(updateData);
        }
        else {
            const createData = {
                name: currentRole.name || "",
                description: currentRole.description || "",
                permissionsList: currentRole.permissionsList || [],
            };
            createRoleMutation.mutate(createData);
        }
        setDialogOpen(false);
    };
    const handleDelete = (id) => {
        setRoleToDelete(id);
        setConfirmOpen(true);
    };
    const handleConfirmDelete = () => {
        if (roleToDelete) {
            deleteRoleMutation.mutate(roleToDelete);
            setConfirmOpen(false);
            setRoleToDelete(null);
        }
    };
    // Error handling
    if (rolesError) {
        return (_jsx("div", { className: "p-6", children: _jsx(Card, { children: _jsx(CardContent, { className: "p-6", children: _jsxs("div", { className: "text-center", children: [_jsx("h3", { className: "text-lg font-medium text-destructive mb-2", children: t("roles.error") }), _jsx("p", { className: "text-sm text-muted-foreground", children: rolesError.message ||
                                    "Failed to load roles. Please try again." })] }) }) }) }));
    }
    return (_jsxs("div", { className: "space-y-6", children: [_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsxs("div", { className: "flex justify-between items-center", children: [_jsx(CardTitle, { className: "text-2xl font-bold", children: t("roles.title") }), _jsxs(Button, { onClick: openCreateDialog, disabled: isMutating, className: "flex items-center gap-2", children: [_jsx(Plus, { className: "w-4 h-4" }), t("roles.create")] })] }) }), _jsx(CardContent, { children: isLoading || isMutating ? (_jsxs("div", { className: "flex justify-center items-center min-h-[200px]", children: [_jsx(Loader2, { className: "h-8 w-8 animate-spin mr-2" }), _jsx("span", { children: t("roles.loading") })] })) : (_jsx("div", { className: "grid grid-cols-1 gap-4", children: roles.map((role) => (_jsx(Card, { className: "border", children: _jsx(CardContent, { className: "p-4", children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("div", { className: "flex gap-3 items-center", children: [_jsx("div", { className: "bg-blue-100 p-2 rounded-lg", children: _jsx(Shield, { className: "w-5 h-5 text-blue-600" }) }), _jsxs("div", { children: [_jsx("h3", { className: "font-semibold text-lg", children: role.name }), _jsx("p", { className: "text-sm text-muted-foreground mb-2", children: role.description }), _jsx("div", { className: "flex flex-wrap gap-1 mt-2", children: (role.permissionsList || []).map((permission) => (_jsx(Badge, { variant: "secondary", className: "text-xs", children: permission.description }, permission._id))) })] })] }), _jsxs("div", { className: "flex gap-2", children: [_jsx(Button, { variant: "outline", size: "sm", onClick: () => openEditDialog(role), disabled: isMutating, children: _jsx(EditIcon, { className: "w-4 h-4" }) }), _jsx(Button, { variant: "outline", size: "sm", onClick: () => handleDelete(role._id || ""), disabled: isMutating, className: "text-destructive hover:text-destructive-foreground", children: _jsx(Trash2, { className: "w-4 h-4" }) })] })] }) }) }, role._id))) })) })] }), _jsx(AddEditRoleModal, { open: dialogOpen, onClose: () => setDialogOpen(false), role: currentRole, setRole: setCurrentRole, isEdit: isEdit, onSave: handleSave, availablePermissions: permissions }), _jsx(ConfirmDialog, { open: confirmOpen, onCancel: () => setConfirmOpen(false), onConfirm: handleConfirmDelete, title: t("roles.delete"), description: t("roles.deleteMessage"), confirmText: t("roles.deleteConfirm"), cancelText: t("common.cancel") })] }));
};
export default RolesManagement;
