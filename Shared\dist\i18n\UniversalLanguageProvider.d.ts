import { ReactNode } from 'react';
export interface Language {
    code: string;
    name: string;
    nativeName: string;
    flag?: string;
    rtl?: boolean;
}
export interface LanguageConfig {
    defaultLanguage?: string;
    storageKey?: string;
    languages?: Language[];
    fallbackLanguage?: string;
    detectBrowserLanguage?: boolean;
}
export interface LanguageProviderState {
    language: string;
    setLanguage: (language: string) => void;
    languages: Language[];
    currentLanguage: Language | undefined;
    isRTL: boolean;
    t: (key: string, params?: Record<string, any>) => string;
}
interface LanguageProviderProps extends LanguageConfig {
    children: ReactNode;
    translations?: Record<string, Record<string, string>>;
    onLanguageChange?: (language: string) => void;
}
export declare const defaultLanguages: Language[];
export declare function LanguageProvider({ children, defaultLanguage, storageKey, languages, fallbackLanguage, detectBrowserLanguage, translations, onLanguageChange, }: LanguageProviderProps): import("react/jsx-runtime").JSX.Element;
export declare const useLanguage: () => LanguageProviderState;
export declare const languageUtils: {
    getStoredLanguage: (storageKey?: string) => string | null;
    setStoredLanguage: (language: string, storageKey?: string) => void;
    detectBrowserLanguage: (availableLanguages: Language[]) => string;
    getLanguageByCode: (code: string, languages: Language[]) => Language | undefined;
    isRTLLanguage: (code: string, languages: Language[]) => boolean;
};
export default LanguageProvider;
