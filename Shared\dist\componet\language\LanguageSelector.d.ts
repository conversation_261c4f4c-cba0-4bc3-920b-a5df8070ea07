interface LanguageSelectorProps {
    variant?: 'select' | 'buttons' | 'cards' | 'icon';
    size?: 'sm' | 'default' | 'lg';
    showNativeNames?: boolean;
    showFlags?: boolean;
    showCodes?: boolean;
    className?: string;
    maxDisplayLanguages?: number;
}
export declare function LanguageSelector({ variant, size, showNativeNames, showFlags, showCodes, className, maxDisplayLanguages, }: LanguageSelectorProps): import("react/jsx-runtime").JSX.Element;
export declare function LanguageToggleButton(): import("react/jsx-runtime").JSX.Element;
export declare function LanguageSettings(): import("react/jsx-runtime").JSX.Element;
export declare function LanguageSwitcher(): import("react/jsx-runtime").JSX.Element;
export declare function LanguageDropdown(): import("react/jsx-runtime").JSX.Element;
export default LanguageSelector;
